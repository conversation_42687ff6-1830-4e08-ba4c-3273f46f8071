import FormField from '@/components/forms/FormField'
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload'
import { FormSelect } from '@/components/forms/FormSelect'
import { FormSlug, sanitizeSlug } from '@/components/forms/FormSlug'
import { FormSwitch } from '@/components/forms/FormSwitch'
import { FormTextField } from '@/components/forms/FormTextField'
import { FormPhoneInput, phoneSchema } from '@/components/forms/FormPhoneInput'
import FormLocation, { locationSchema } from '@/components/forms/FormLocation'
import { FormSlider } from '@/components/forms/FormSlider'
import { FormComboBox } from '@/components/forms/FormComboBox'
import { FormComboBoxPopover } from '@/components/forms/FormComboPopover'
import { FormBusinessHours } from '@/components/forms/FormBusinessHours'
import { Button } from '@/components/ui/button'
import {
  ClubStatus,
  DayOfWeek,
  MenuItemAvailability,
  useCreateClubMutation,
  useCitiesQuery,
  useNeighborhoodsQuery,
  useClubCategoriesQuery,
} from '@/generated/graphql'
import { toastPromise } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import { Plus, Trash2 } from 'lucide-react'
import {
  FormProvider,
  useFieldArray,
  useForm,
  type SubmitHandler,
  type UseFormReturn,
} from 'react-hook-form'
import { useMemo, useEffect, useRef } from 'react'
import * as z from 'zod'

export const DayTimingSchema = z.object({
  day: z.nativeEnum(DayOfWeek),
  timings: z
    .tuple([z.coerce.number(), z.coerce.number()])
    .refine(([openTime, closeTime]) => {
      // Validate HHMM format for both times
      const isValidOpen =
        openTime >= 0 &&
        openTime <= 2359 &&
        Math.floor(openTime / 100) <= 23 &&
        openTime % 100 <= 59
      const isValidClose =
        closeTime >= 0 &&
        closeTime <= 2359 &&
        Math.floor(closeTime / 100) <= 23 &&
        closeTime % 100 <= 59
      return isValidOpen && isValidClose
    }, 'Both opening and closing times must be valid HHMM format (0000-2359)'),
})

/** BusinessHoursSchedule */
export const BusinessHoursScheduleSchema = z.object({
  schedule: z.array(DayTimingSchema),
})

/** MenuItem */
export const MenuItemSchema = z.object({
  name: z.string().min(1, 'Item name is required'),
  price: z.coerce.number().min(0, 'Price must be 0 or greater'),
  available: z.nativeEnum(MenuItemAvailability),
})

/** MenuSection */
export const MenuSectionSchema = z.object({
  name: z.string().min(1, 'Section name is required'),
  items: z.array(MenuItemSchema),
})

/** Menu */
export const MenuSchema = z.object({
  currency: z.string(),
  sections: z.array(MenuSectionSchema),
})

/** Contact Schema - using the exported phoneSchema */
export const ContactSchema = phoneSchema

/** Address Schema - using the imported locationSchema */
export const AddressSchema = z.object({
  address: z.string().min(1, 'Address is required'),
  location: locationSchema,
  metroLine: z.string().optional(),
  metroStation: z.string().optional(),
})

/** Club Schema */
export const ClubSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  slug: z.string().min(1, 'Slug is required'),
  description: z.string().min(1, 'Description is required'),
  categories: z.array(z.string()).min(1, 'At least one category is required'),
  city: z.string().min(1, 'City is required'),
  neighborhood: z.string().optional(),
  status: z.nativeEnum(ClubStatus),
  logo: z.string().optional(),
  coverImage: z.string().optional(),
  images: z.array(z.string()),
  contact: ContactSchema.optional(),
  address: AddressSchema.optional(),
  businessHours: BusinessHoursScheduleSchema.optional(),
  menu: MenuSchema.optional(),
  featured: z.boolean(),
  rating: z.coerce.number().min(0).max(5),
  location: locationSchema,
})

export type CreateClubForm = z.infer<typeof ClubSchema>

export type CreateClubFormProps = {
  methods: UseFormReturn<CreateClubForm>
  onSubmit: SubmitHandler<CreateClubForm>
}

// Component for managing individual menu sections
function MenuSectionComponent({
  sectionIndex,
  methods,
  onRemoveSection,
}: {
  sectionIndex: number
  methods: UseFormReturn<CreateClubForm>
  onRemoveSection: () => void
}) {
  const {
    append: menuItemAppend,
    remove: menuItemRemove,
    fields: menuItemFields,
  } = useFieldArray({
    control: methods.control,
    name: `menu.sections.${sectionIndex}.items`,
  })

  return (
    <div className="border rounded-lg p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h5 className="text-sm font-medium">Menu Section {sectionIndex + 1}</h5>
        <Button
          onClick={onRemoveSection}
          type="button"
          variant="destructive"
          size="sm"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      {/* Section Name */}
      <FormField
        name={`menu.sections.${sectionIndex}.name`}
        label="Section Name"
        placeholder="e.g., Appetizers, Main Courses, Beverages"
      />

      {/* Menu Items in this section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h6 className="text-xs font-medium text-muted-foreground">
            Items in this section
          </h6>
          <Button
            onClick={() =>
              menuItemAppend({
                available: MenuItemAvailability.Available,
                name: '',
                price: 0,
              })
            }
            type="button"
            variant="outline"
            size="sm"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Item
          </Button>
        </div>

        {menuItemFields.map((item, itemIdx) => (
          <div key={item.id} className="border-l-2 border-muted pl-4 space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">
                Item {itemIdx + 1}
              </span>
              <Button
                onClick={() => menuItemRemove(itemIdx)}
                type="button"
                variant="ghost"
                size="sm"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <FormField
                name={`menu.sections.${sectionIndex}.items.${itemIdx}.name`}
                label="Item Name"
                placeholder="Enter menu item name"
              />
              <FormField
                name={`menu.sections.${sectionIndex}.items.${itemIdx}.price`}
                label="Price"
                placeholder="Enter price"
                type="number"
                min={0}
                step={0.01}
                onChange={(e) => {
                  const value =
                    e.target.value === '' ? 0 : parseFloat(e.target.value)
                  methods.setValue(
                    `menu.sections.${sectionIndex}.items.${itemIdx}.price`,
                    value,
                  )
                }}
              />
              <FormSelect
                name={`menu.sections.${sectionIndex}.items.${itemIdx}.available`}
                label="Availability"
                options={Object.values(MenuItemAvailability).map((k) => ({
                  value: k,
                  label: k,
                }))}
                placeholder="Select availability"
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export const useCreateClubFormMethods = () => {
  const { mutateAsync: createClub } = useCreateClubMutation()
  const methods = useForm<CreateClubForm>({
    defaultValues: {
      address: {
        address: '',
        location: {
          center: [48.8566, 2.3522], // Default to Paris coordinates
        },
        metroLine: '',
        metroStation: '',
      },
      businessHours: {
        schedule: [
          {
            day: DayOfWeek.Monday,
            timings: [900, 1700] as [number, number], // 9:00 AM to 5:00 PM
          },
          {
            day: DayOfWeek.Tuesday,
            timings: [900, 1700] as [number, number],
          },
          {
            day: DayOfWeek.Wednesday,
            timings: [900, 1700] as [number, number],
          },
          {
            day: DayOfWeek.Thursday,
            timings: [900, 1700] as [number, number],
          },
          {
            day: DayOfWeek.Friday,
            timings: [900, 1700] as [number, number],
          },
          {
            day: DayOfWeek.Saturday,
            timings: [1000, 1600] as [number, number], // 10:00 AM to 4:00 PM
          },
          {
            day: DayOfWeek.Sunday,
            timings: [1100, 1500] as [number, number], // 11:00 AM to 3:00 PM
          },
        ],
      },
      categories: [],
      city: '',
      neighborhood: '',
      coverImage: '',
      description: '',
      featured: false,
      images: [],
      logo: '',
      menu: {
        currency: '€',
        sections: [
          {
            name: 'Main Menu',
            items: [
              {
                name: '',
                price: 0,
                available: MenuItemAvailability.Available,
              },
            ],
          },
        ],
      },
      name: '',
      contact: {
        countryCode: '+1',
        phone: '',
      },
      rating: 0,
      slug: '',
      status: ClubStatus.Active,
    },
    resolver: zodResolver(ClubSchema),
  })

  const onSubmit: SubmitHandler<CreateClubForm> = (data) => {
    toastPromise({
      asyncFunc: createClub({ input: data }),
      success: 'Club created successfully',
      error: 'Failed to create club',
      onSuccess() {
        methods.reset()
      },
      onError(err) {
        console.error(err)
        // Check if this is the specific GraphQL serialization error for createClub
        // The club is actually created successfully, but GraphQL can't serialize the response
        if (
          err instanceof Error &&
          err.message.includes('Boolean cannot represent a non boolean value')
        ) {
          // Treat this as a success since the club was actually created
          methods.reset()
          // Show success toast manually
          import('react-hot-toast').then(({ toast }) => {
            toast.success('Club created successfully')
          })
        }
      },
    })
  }

  return { methods, onSubmit }
}

export default function CreateClubForm({
  methods,
  onSubmit,
}: CreateClubFormProps) {
  // Watch the selected city to filter neighborhoods
  const selectedCity = methods.watch('city')

  // Fetch cities for the dropdown with caching optimization
  const { data: citiesData, isLoading: citiesLoading } = useCitiesQuery(
    {
      paginationInput: { limit: 100, page: 1 }, // Get all cities for dropdown
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes - cities don't change often
      gcTime: 10 * 60 * 1000, // 10 minutes (garbage collection time)
      refetchOnWindowFocus: false,
    },
  )

  // Only fetch neighborhoods when a city is actually selected
  // Add enabled option to prevent unnecessary queries
  const { data: neighborhoodsData, isLoading: neighborhoodsLoading } =
    useNeighborhoodsQuery(
      {
        neighborhoodsInput: selectedCity ? { city: selectedCity } : undefined,
        paginationInput: { limit: 100, page: 1 },
      },
      {
        enabled: !!selectedCity, // Only run query when city is selected
        staleTime: 2 * 60 * 1000, // 2 minutes
        gcTime: 5 * 60 * 1000, // 5 minutes (garbage collection time)
      },
    )

  // Fetch club categories for the dropdown with caching optimization
  const { data: clubCategoriesData, isLoading: clubCategoriesLoading } =
    useClubCategoriesQuery(
      {
        paginationInput: { limit: 100, page: 1 }, // Get all categories for dropdown
        clubCategoriesInput: {},
      },
      {
        staleTime: 10 * 60 * 1000, // 10 minutes - categories don't change often
        gcTime: 15 * 60 * 1000, // 15 minutes (garbage collection time)
        refetchOnWindowFocus: false,
      },
    )

  // Memoize city options to prevent unnecessary re-renders
  const cityOptions = useMemo(
    () =>
      citiesData?.cities.docs.map((city) => ({
        value: city.id,
        label: city.name,
      })) || [],
    [citiesData?.cities.docs],
  )

  // Memoize neighborhood options to prevent unnecessary re-renders
  const neighborhoodOptions = useMemo(
    () =>
      neighborhoodsData?.neighborhoods.docs.map((neighborhood) => ({
        value: neighborhood.id,
        label: neighborhood.name,
      })) || [],
    [neighborhoodsData?.neighborhoods.docs],
  )

  // Memoize club categories options to prevent unnecessary re-renders
  const clubCategoriesOptions = useMemo(
    () =>
      clubCategoriesData?.clubCategories.docs.map((category) => ({
        value: category.id,
        label: category.name,
      })) || [],
    [clubCategoriesData?.clubCategories.docs],
  )

  // Use ref to track previous city value to avoid unnecessary neighborhood clearing
  const previousCityRef = useRef<string | undefined>(undefined)

  // Efficiently clear neighborhood when city changes
  useEffect(() => {
    if (selectedCity && selectedCity !== previousCityRef.current) {
      // Only clear neighborhood if city actually changed
      if (previousCityRef.current !== undefined) {
        methods.setValue('neighborhood', '')
      }
      previousCityRef.current = selectedCity
    }
  }, [selectedCity, methods])

  // Field arrays for menu sections and items
  const {
    append: menuSectionAppend,
    remove: menuSectionRemove,
    fields: menuSectionFields,
  } = useFieldArray({
    control: methods.control,
    name: 'menu.sections',
  })

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className="w-full space-y-8 p-10"
      >
        {/* Basic Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Basic Information</h3>
          <div className="grid grid-cols-2 gap-4">
            {/* Left Column */}
            <div className="space-y-4">
              <FormField
                name="name"
                label="Name"
                placeholder="Enter club name"
                onChange={(e) => {
                  methods.setValue('name', e.target.value)
                  const slug = sanitizeSlug(e.target.value)
                  methods.setValue('slug', slug)
                }}
              />
              <FormSlug
                name="slug"
                label="Slug"
                placeholder="Enter club slug"
              />
              <FormTextField
                name="description"
                label="Description"
                placeholder="Enter club description"
              />
              <div className="grid grid-cols-2 gap-4">
                <FormComboBox
                  name="city"
                  label="City"
                  placeholder="Select a city"
                  options={cityOptions}
                  disabled={citiesLoading}
                />
                <FormComboBox
                  name="neighborhood"
                  label="Neighborhood (Optional)"
                  placeholder={
                    selectedCity
                      ? 'Select a neighborhood'
                      : 'Select a city first'
                  }
                  options={neighborhoodOptions}
                  disabled={!selectedCity || neighborhoodsLoading}
                />
              </div>
              <FormSelect
                name="status"
                label="Status"
                options={Object.values(ClubStatus).map((status) => ({
                  value: status,
                  label: status,
                }))}
                placeholder="Select club status"
              />
              <div className="grid grid-cols-2 gap-4">
                <FormSlider
                  name="rating"
                  label="Rating"
                  description="Set the club rating from 0 to 5 stars"
                  min={0}
                  max={5}
                  step={0.1}
                  showValue={true}
                />
                <FormSwitch name="featured" label="Featured Club" />
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <FormLocation
                name="location"
                label="Club Location"
                placeholder="Search club location"
              />
            </div>
          </div>
        </div>

        {/* Media Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Media</h3>
          <div className="grid grid-cols-3 gap-4">
            <FormPresignedUpload
              name="coverImage"
              label="Cover Image"
              accept="image/*"
            />
            <FormPresignedUpload name="logo" label="Logo" accept="image/*" />
            <FormPresignedUpload
              name="images"
              label="Gallery Images"
              accept="image/*"
              multiple={true}
            />
          </div>
        </div>

        {/* Address Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Address</h3>
          <div className="grid grid-cols-3 gap-4 border p-4 rounded-xl">
            <FormTextField
              name="address.address"
              label="Street Address"
              placeholder="Enter full address"
            />
            <div className="space-y-2">
              <div className="space-y-2">
                <FormField
                  name="address.metroStation"
                  label="Metro Station"
                  placeholder="Enter metro station (optional)"
                />
                <FormField
                  name="address.metroLine"
                  label="Metro Line"
                  placeholder="Enter metro line (optional)"
                />
              </div>
            </div>
            <FormPhoneInput
              name="contact"
              label="Contact Number"
              placeholder="Enter phone number"
              description=" "
            />
          </div>
        </div>

        {/* Categories Section */}
        <div className="grid grid-cols-3">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Categories</h3>
            <FormComboBoxPopover
              name="categories"
              label=""
              placeholder="Search categories..."
              options={clubCategoriesOptions}
              multiple={true}
              disabled={clubCategoriesLoading}
            />
          </div>
        </div>

        {/* Menu Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Menu</h3>

          <div className="grid grid-cols-3 gap-4">
            <FormField
              name="menu.currency"
              label="Currency"
              placeholder="e.g., $, €, ₹"
            />
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-md font-medium">Menu Sections</h4>
              <Button
                onClick={() =>
                  menuSectionAppend({
                    name: '',
                    items: [
                      {
                        available: MenuItemAvailability.Available,
                        name: '',
                        price: 0,
                      },
                    ],
                  })
                }
                type="button"
                variant="outline"
                size="sm"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Menu Section
              </Button>
            </div>

            {menuSectionFields.map((section, sectionIdx) => (
              <MenuSectionComponent
                key={section.id}
                sectionIndex={sectionIdx}
                methods={methods}
                onRemoveSection={() => menuSectionRemove(sectionIdx)}
              />
            ))}
          </div>
        </div>

        {/* Business Hours Section */}
        <FormBusinessHours
          name="businessHours"
          label="Business Hours"
          description="Set opening and closing times for each day of the week"
        />

        {/* Submit Button */}
        <div className="flex justify-end pt-6">
          <Button type="submit" size="lg" className="min-w-32">
            Create Club
          </Button>
        </div>
      </form>
    </FormProvider>
  )
}
