import { Button } from '@/components/ui/button'
import {
  useUpdateClubMutation,
  useClubsQuery,
  useCitiesQuery,
  useNeighborhoodsQuery,
  useClubCategoriesQuery,
  ClubStatus,
  MenuItemAvailability,
} from '@/generated/graphql'
import { toastPromise } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import type { SubmitHandler, UseFormReturn } from 'react-hook-form'
import { FormProvider, useForm } from 'react-hook-form'
import { useEffect } from 'react'

// Import the existing club schema and components from create form
import { ClubSchema } from './create-club-form'
import { z } from 'zod'

// Update schema to include id field
const UpdateClubSchema = ClubSchema.extend({
  id: z.string(),
})

type UpdateClubForm = z.infer<typeof UpdateClubSchema>

type UpdateClubFormProps = {
  methods: UseFormReturn<UpdateClubForm>
  onSubmit: SubmitHandler<UpdateClubForm>
  isLoading?: boolean
}

export const useUpdateClubFormMethods = (clubSlug: string) => {
  const { mutateAsync: updateClub } = useUpdateClubMutation()
  
  // Fetch club data using the clubs query with slug filter
  const { data: clubsData, isLoading } = useClubsQuery({
    clubsInput: { slug: clubSlug },
  }, {
    initialData: { clubs: { docs: [], totalDocs: 0, limit: 10, page: 1, totalPages: 0, prevPage: null, nextPage: null, hasPrevPage: false, hasNextPage: false, pagingCounter: 1 } }
  })

  const methods = useForm<UpdateClubForm>({
    defaultValues: {
      id: '',
      address: {
        address: '',
        location: {
          center: [48.8566, 2.3522],
        },
        metroLine: '',
        metroStation: '',
      },
      businessHours: {
        schedule: [
          { day: 'MONDAY', timings: [] },
          { day: 'TUESDAY', timings: [] },
          { day: 'WEDNESDAY', timings: [] },
          { day: 'THURSDAY', timings: [] },
          { day: 'FRIDAY', timings: [] },
          { day: 'SATURDAY', timings: [] },
          { day: 'SUNDAY', timings: [] },
        ],
      },
      categories: [],
      city: '',
      neighborhood: '',
      coverImage: '',
      description: '',
      featured: false,
      images: [],
      logo: '',
      menu: {
        currency: '€',
        sections: [
          {
            name: 'Main Menu',
            items: [
              {
                name: '',
                price: 0,
                available: MenuItemAvailability.Available,
              },
            ],
          },
        ],
      },
      name: '',
      contact: {
        countryCode: '+1',
        phone: '',
      },
      rating: 0,
      slug: '',
      status: ClubStatus.Active,
      location: {
        center: [48.8566, 2.3522],
      },
    },
    resolver: zodResolver(UpdateClubSchema),
  })

  // Populate form when club data is loaded
  useEffect(() => {
    if (clubsData?.clubs.docs.length > 0) {
      const club = clubsData.clubs.docs[0]
      // Note: We'll need to fetch more detailed club data for full form population
      // For now, populate basic fields available from the clubs query
      methods.reset({
        id: club.id,
        name: club.name,
        slug: club.slug,
        coverImage: club.coverImage || '',
        images: club.images || [],
        city: club.city.id,
        neighborhood: club.neighborhood?.id || '',
        // Add other fields as they become available from the query
        // For now, keep defaults for complex fields
        address: {
          address: '',
          location: {
            center: [48.8566, 2.3522],
          },
          metroLine: '',
          metroStation: '',
        },
        businessHours: {
          schedule: [
            { day: 'MONDAY', timings: [] },
            { day: 'TUESDAY', timings: [] },
            { day: 'WEDNESDAY', timings: [] },
            { day: 'THURSDAY', timings: [] },
            { day: 'FRIDAY', timings: [] },
            { day: 'SATURDAY', timings: [] },
            { day: 'SUNDAY', timings: [] },
          ],
        },
        categories: [],
        description: '',
        featured: false,
        logo: '',
        menu: {
          currency: '€',
          sections: [
            {
              name: 'Main Menu',
              items: [
                {
                  name: '',
                  price: 0,
                  available: MenuItemAvailability.Available,
                },
              ],
            },
          ],
        },
        contact: {
          countryCode: '+1',
          phone: '',
        },
        rating: 0,
        status: ClubStatus.Active,
        location: {
          center: [48.8566, 2.3522],
        },
      })
    }
  }, [clubsData, methods])

  const onSubmit: SubmitHandler<UpdateClubForm> = (data) => {
    toastPromise({
      asyncFunc: updateClub({ input: data }),
      success: 'Club updated successfully',
      error: 'Failed to update club',
      onSuccess() {
        // Optionally redirect or refresh data
      },
      onError(err) {
        console.error(err)
      },
    })
  }

  return { methods, onSubmit, isLoading }
}

// For now, create a simple update form component
// We'll need to import and use the full CreateClubForm components
export default function UpdateClubForm({
  methods,
  onSubmit,
  isLoading = false,
}: UpdateClubFormProps) {
  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Update Club</h2>
          <p className="text-muted-foreground">
            Update club information. Full form implementation coming soon.
          </p>
        </div>
        
        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? 'Loading...' : 'Update Club'}
        </Button>
      </form>
    </FormProvider>
  )
}
