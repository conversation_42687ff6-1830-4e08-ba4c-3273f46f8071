import FormField from '@/components/forms/FormField'
import FormLocation, { locationSchema } from '@/components/forms/FormLocation'
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload'
import { FormTextField } from '@/components/forms/FormTextField'

import { Button } from '@/components/ui/button'
import { useCreateCityMutation } from '@/generated/graphql'
import { toastPromise } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import type { SubmitHandler, UseFormReturn } from 'react-hook-form'
import { FormProvider, useForm } from 'react-hook-form'
import { z } from 'zod'

const schema = z.object({
  name: z.string(),
  image: z.string().nonempty(),
  coverImage: z.string().nonempty(),
  heading: z.string().nonempty(),
  subHeading: z.string().nonempty(),
  location: locationSchema,
})

type CreateCityForm = z.infer<typeof schema>

type CreateCityFormProps = {
  methods: UseFormReturn<CreateCityForm>
  onSubmit: SubmitHandler<CreateCityForm>
}

export const useCreateCityFormMethods = () => {
  const { mutateAsync: createCity } = useCreateCityMutation()
  const methods = useForm<CreateCityForm>({
    defaultValues: {
      coverImage: '',
      heading: '',
      image: '',
      name: '',
      subHeading: '',
      location: {
        center: [48.8566, 2.3522],
      },
    },
    resolver: zodResolver(schema),
  })

  const onSubmit: SubmitHandler<CreateCityForm> = (data) => {
    toastPromise({
      asyncFunc: createCity({ input: data }),
      success: 'City created successfully',
      error: 'Failed to create city',
      onSuccess() {
        methods.reset()
      },
      onError(err) {
        console.error(err)
      },
    })
  }

  return { methods, onSubmit }
}

export default function CreateCityForm({
  methods,
  onSubmit,
}: CreateCityFormProps) {
  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className="w-full grid grid-cols-2 gap-5 p-10"
      >
        <div className="space-y-4">
          <FormField name="name" label="Name" placeholder="Enter city name" />

          <FormPresignedUpload
            name="coverImage"
            label="Cover Image"
            accept="image/*"
          />
          <FormPresignedUpload
            name="image"
            label="Thumbnail"
            accept="image/*"
          />
          <FormTextField
            name="heading"
            label="Page Heading"
            placeholder="Enter city heading"
          />
          <FormTextField
            name="subHeading"
            label="Page Sub Heading"
            placeholder="Enter city sub heading"
          />

          <Button type="submit" className="w-full">
            Submit
          </Button>
        </div>

        {/* Form */}
        <div>
          <FormLocation
            label="Location"
            name="location"
            placeholder="Search location"
          />
        </div>
      </form>
    </FormProvider>
  )
}
