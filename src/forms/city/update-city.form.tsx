import FormField from '@/components/forms/FormField'
import FormLocation, { locationSchema } from '@/components/forms/FormLocation'
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload'
import { FormTextField } from '@/components/forms/FormTextField'

import { Button } from '@/components/ui/button'
import { useUpdateCityMutation, useCitiesQuery } from '@/generated/graphql'
import { toastPromise } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import type { SubmitHandler, UseFormReturn } from 'react-hook-form'
import { FormProvider, useForm } from 'react-hook-form'
import { z } from 'zod'
import { useEffect } from 'react'

const schema = z.object({
  id: z.string(),
  name: z.string(),
  image: z.string().nonempty(),
  coverImage: z.string().nonempty(),
  heading: z.string().nonempty(),
  subHeading: z.string().nonempty(),
  location: locationSchema,
})

type UpdateCityForm = z.infer<typeof schema>

type UpdateCityFormProps = {
  methods: UseFormReturn<UpdateCityForm>
  onSubmit: SubmitHandler<UpdateCityForm>
  isLoading?: boolean
}

export const useUpdateCityFormMethods = (cityName: string) => {
  const { mutateAsync: updateCity } = useUpdateCityMutation()

  // Fetch city data using the cities query with name filter
  const { data: citiesData, isLoading } = useCitiesQuery(
    {
      citiesInput: { name: cityName },
      paginationInput: { limit: 1, page: 1 },
    },
    {
      initialData: {
        cities: {
          docs: [],
          totalDocs: 0,
          limit: 1,
          page: 1,
          totalPages: 0,
          prevPage: null,
          nextPage: null,
          hasPrevPage: false,
          hasNextPage: false,
          pagingCounter: 1,
        },
      },
    },
  )

  const methods = useForm<UpdateCityForm>({
    defaultValues: {
      id: '',
      coverImage: '',
      heading: '',
      image: '',
      name: '',
      subHeading: '',
      location: {
        center: [48.8566, 2.3522],
      },
    },
    resolver: zodResolver(schema),
  })

  // Populate form when city data is loaded
  useEffect(() => {
    if (citiesData?.cities?.docs && citiesData.cities.docs.length > 0) {
      const city = citiesData.cities.docs[0]
      methods.reset({
        id: city.id,
        name: city.name,
        image: city.image,
        coverImage: city.coverImage || '',
        heading: city.heading,
        subHeading: city.subHeading || '',
        location: city.location,
      })
    }
  }, [citiesData, methods])

  const onSubmit: SubmitHandler<UpdateCityForm> = (data) => {
    toastPromise({
      asyncFunc: updateCity({ input: data }),
      success: 'City updated successfully',
      error: 'Failed to update city',
      onSuccess() {
        // Optionally redirect or refresh data
      },
      onError(err) {
        console.error(err)
      },
    })
  }

  return { methods, onSubmit, isLoading }
}

export default function UpdateCityForm({
  methods,
  onSubmit,
  isLoading = false,
}: UpdateCityFormProps) {
  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className="w-full grid grid-cols-2 gap-5 p-10"
      >
        <div className="space-y-4">
          <FormField name="name" label="Name" placeholder="Enter city name" />

          <FormPresignedUpload
            name="coverImage"
            label="Cover Image"
            accept="image/*"
          />
          <FormPresignedUpload
            name="image"
            label="Thumbnail"
            accept="image/*"
          />
          <FormTextField
            name="heading"
            label="Page Heading"
            placeholder="Enter city heading"
          />
          <FormTextField
            name="subHeading"
            label="Page Sub Heading"
            placeholder="Enter city sub heading"
          />

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? 'Loading...' : 'Update City'}
          </Button>
        </div>

        {/* Form */}
        <div>
          <FormLocation
            label="Location"
            name="location"
            placeholder="Search location"
          />
        </div>
      </form>
    </FormProvider>
  )
}
