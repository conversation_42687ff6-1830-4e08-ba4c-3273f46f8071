import FormField from '@/components/forms/FormField'
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload'
import { FormTextField } from '@/components/forms/FormTextField'
import { Button } from '@/components/ui/button'
import {
  useUpdateClubCategoryMutation,
  useClubCategoriesQuery,
} from '@/generated/graphql'
import { toastPromise } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import type { SubmitHandler, UseFormReturn } from 'react-hook-form'
import { FormProvider, useForm } from 'react-hook-form'
import { z } from 'zod'
import { useEffect } from 'react'

// Import the existing club category schema from create form
import { ClubCategorySchema } from './create-club-category-form'

// Update schema to include id field
const UpdateClubCategorySchema = ClubCategorySchema.extend({
  id: z.string(),
})

type UpdateClubCategoryForm = z.infer<typeof UpdateClubCategorySchema>

type UpdateClubCategoryFormProps = {
  methods: UseFormReturn<UpdateClubCategoryForm>
  onSubmit: SubmitHandler<UpdateClubCategoryForm>
  isLoading?: boolean
}

export const useUpdateClubCategoryFormMethods = (categoryName: string) => {
  const { mutateAsync: updateClubCategory } = useUpdateClubCategoryMutation()

  // Fetch club category data using the clubCategories query with name filter
  const { data: clubCategoriesData, isLoading } = useClubCategoriesQuery(
    {
      clubCategoriesInput: { name: categoryName },
      paginationInput: { limit: 1, page: 1 },
    },
    {
      initialData: {
        clubCategories: {
          docs: [],
          totalDocs: 0,
          limit: 1,
          page: 1,
          totalPages: 0,
          prevPage: null,
          nextPage: null,
          hasPrevPage: false,
          hasNextPage: false,
          pagingCounter: 1,
        },
      },
    },
  )

  const methods = useForm<UpdateClubCategoryForm>({
    defaultValues: {
      id: '',
      name: '',
      description: '',
      image: '',
      icon: '',
    },
    resolver: zodResolver(UpdateClubCategorySchema),
  })

  // Populate form when club category data is loaded
  useEffect(() => {
    if (clubCategoriesData?.clubCategories?.docs && clubCategoriesData.clubCategories.docs.length > 0) {
      const category = clubCategoriesData.clubCategories.docs[0]
      methods.reset({
        id: category.id,
        name: category.name,
        description: category.description || '',
        image: category.image || '',
        icon: category.icon || '',
      })
    }
  }, [clubCategoriesData, methods])

  const onSubmit: SubmitHandler<UpdateClubCategoryForm> = (data) => {
    // Filter out empty optional fields
    const cleanedData = {
      id: data.id,
      ...(data.name && { name: data.name }),
      ...(data.description && { description: data.description }),
      ...(data.image && { image: data.image }),
      ...(data.icon && { icon: data.icon }),
    }

    toastPromise({
      asyncFunc: updateClubCategory({ input: cleanedData }),
      success: 'Club category updated successfully',
      error: 'Failed to update club category',
      onSuccess() {
        // Optionally redirect or refresh data
      },
      onError(err) {
        console.error(err)
      },
    })
  }

  return { methods, onSubmit, isLoading }
}

export default function UpdateClubCategoryForm({
  methods,
  onSubmit,
  isLoading = false,
}: UpdateClubCategoryFormProps) {
  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className="w-full max-w-2xl mx-auto space-y-8 p-6"
      >
        {/* Header */}
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-bold">Update Club Category</h2>
          <p className="text-muted-foreground">
            Modify the details of this club category
          </p>
        </div>

        {/* Basic Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Basic Information</h3>

          <FormField
            name="name"
            label="Category Name"
            placeholder="e.g., Nightclub, Lounge, Dance Club"
            description="Enter a descriptive name for this club category"
          />

          <FormTextField
            name="description"
            label="Description (Optional)"
            placeholder="Describe what types of clubs belong to this category..."
            description="Provide additional details about this category"
            multiline
            rows={3}
          />
        </div>

        {/* Visual Elements Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Visual Elements</h3>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <FormPresignedUpload
                name="image"
                label="Category Image (Optional)"
                accept="image/*"
              />
              <p className="text-sm text-muted-foreground">
                Upload an image to represent this category
              </p>
            </div>

            <FormField
              name="icon"
              label="Lucide Icon Name (Optional)"
              placeholder="e.g., Music, Disc3, PartyPopper, Zap"
              description="Name of a Lucide React icon (without 'Icon' suffix)"
            />
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end pt-6">
          <Button type="submit" size="lg" className="min-w-32" disabled={isLoading}>
            {isLoading ? 'Updating...' : 'Update Category'}
          </Button>
        </div>
      </form>
    </FormProvider>
  )
}
