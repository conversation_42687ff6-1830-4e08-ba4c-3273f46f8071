import FormField from '@/components/forms/FormField'
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload'
import { FormTextField } from '@/components/forms/FormTextField'
import { Button } from '@/components/ui/button'
import { useCreateClubCategoryMutation } from '@/generated/graphql'
import { toastPromise } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  FormProvider,
  useForm,
  type SubmitHandler,
  type UseFormReturn,
} from 'react-hook-form'
import * as z from 'zod'

/** Club Category Schema */
export const ClubCategorySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  image: z.string().optional(),
  icon: z.string().optional(),
})

export type CreateClubCategoryForm = z.infer<typeof ClubCategorySchema>

export type CreateClubCategoryFormProps = {
  methods: UseFormReturn<CreateClubCategoryForm>
  onSubmit: SubmitHandler<CreateClubCategoryForm>
}

export const useCreateClubCategoryFormMethods = () => {
  const { mutateAsync: createClubCategory } = useCreateClubCategoryMutation()
  const methods = useForm<CreateClubCategoryForm>({
    defaultValues: {
      name: '',
      description: '',
      image: '',
      icon: '',
    },
    resolver: zodResolver(ClubCategorySchema),
  })

  const onSubmit: SubmitHandler<CreateClubCategoryForm> = (data) => {
    // Filter out empty optional fields
    const cleanedData = {
      name: data.name,
      ...(data.description && { description: data.description }),
      ...(data.image && { image: data.image }),
      ...(data.icon && { icon: data.icon }),
    }

    toastPromise({
      asyncFunc: createClubCategory({ input: cleanedData }),
      success: 'Club category created successfully',
      error: 'Failed to create club category',
      onSuccess() {
        methods.reset()
      },
      onError(err) {
        console.error(err)
      },
    })
  }

  return { methods, onSubmit }
}

export default function CreateClubCategoryForm({
  methods,
  onSubmit,
}: CreateClubCategoryFormProps) {
  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className="w-full space-y-8 p-10"
      >
        {/* Basic Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Basic Information</h3>

          <FormField
            name="name"
            label="Category Name"
            placeholder="Enter category name"
            description="The name of the club category (e.g., Nightclub, Student Bar, Cocktail Bar, etc)"
          />

          <FormTextField
            name="description"
            label="Description (Optional)"
            placeholder="Enter category description"
            description="A brief description of what this category represents"
          />
        </div>

        {/* Visual Elements Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Visual Elements</h3>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <FormPresignedUpload
                name="image"
                label="Category Image (Optional)"
                accept="image/*"
              />
              <p className="text-sm text-muted-foreground">
                Upload an image to represent this category
              </p>
            </div>

            <FormField
              name="icon"
              label="Lucide Icon Name (Optional)"
              placeholder="e.g., Music, Coffee, Utensils"
              description="Name of a Lucide React icon (without 'Icon' suffix)"
            />
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end pt-6">
          <Button type="submit" size="lg" className="min-w-32">
            Create Category
          </Button>
        </div>
      </form>
    </FormProvider>
  )
}
