import FormField from '@/components/forms/FormField'
import FormLocation, { locationSchema } from '@/components/forms/FormLocation'
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload'
import { FormComboBox } from '@/components/forms/FormComboBox'
import { FormSlug, sanitizeSlug } from '@/components/forms/FormSlug'

import { Button } from '@/components/ui/button'
import {
  useCreateNeighborhoodMutation,
  useCitiesQuery,
} from '@/generated/graphql'
import { toastPromise } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import type { SubmitHandler, UseFormReturn } from 'react-hook-form'
import { FormProvider, useForm } from 'react-hook-form'
import { z } from 'zod'

const schema = z.object({
  name: z.string().min(1, 'Name is required'),
  slug: z.string().min(1, 'Slug is required'),
  city: z.string().min(1, 'City is required'),
  coverImage: z.string().optional(),
  images: z.array(z.string()).optional(),
  location: locationSchema,
})

type CreateNeighborhoodForm = z.infer<typeof schema>

type CreateNeighborhoodFormProps = {
  methods: UseFormReturn<CreateNeighborhoodForm>
  onSubmit: SubmitHandler<CreateNeighborhoodForm>
}

export const useCreateNeighborhoodFormMethods = () => {
  const { mutateAsync: createNeighborhood } = useCreateNeighborhoodMutation()
  const methods = useForm<CreateNeighborhoodForm>({
    defaultValues: {
      name: '',
      slug: '',
      city: '',
      coverImage: '',
      images: [],
      location: {
        center: [48.8566, 2.3522],
      },
    },
    resolver: zodResolver(schema),
  })

  const onSubmit: SubmitHandler<CreateNeighborhoodForm> = (data) => {
    toastPromise({
      asyncFunc: createNeighborhood({ input: data }),
      success: 'Neighborhood created successfully',
      error: 'Failed to create neighborhood',
      onSuccess() {
        methods.reset()
      },
      onError(err) {
        console.error(err)
      },
    })
  }

  return { methods, onSubmit }
}

export default function CreateNeighborhoodForm({
  methods,
  onSubmit,
}: CreateNeighborhoodFormProps) {
  // Fetch cities for the dropdown
  const { data: citiesData, isLoading: citiesLoading } = useCitiesQuery({
    paginationInput: { limit: 100, page: 1 }, // Get all cities for dropdown
  })

  // Transform cities data for the select component
  const cityOptions =
    citiesData?.cities.docs.map((city) => ({
      value: city.id,
      label: city.name,
    })) || []

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className="w-full grid grid-cols-2 gap-5 p-10"
      >
        <div className="space-y-4">
          <FormField
            name="name"
            label="Name"
            placeholder="Enter neighborhood name"
            onChange={(e) => {
              methods.setValue('name', e.target.value)
              const slug = sanitizeSlug(e.target.value)
              methods.setValue('slug', slug)
            }}
          />

          <FormSlug
            name="slug"
            label="Slug"
            placeholder="Enter neighborhood slug"
          />

          <FormComboBox
            name="city"
            label="City"
            placeholder="Select a city"
            options={cityOptions}
            disabled={citiesLoading}
          />

          <FormPresignedUpload
            name="coverImage"
            label="Cover Image"
            accept="image/*"
          />

          <Button type="submit" className="w-full">
            Submit
          </Button>
        </div>

        {/* Location Form */}
        <div>
          <FormLocation
            label="Location"
            name="location"
            placeholder="Search location"
          />
        </div>
      </form>
    </FormProvider>
  )
}
