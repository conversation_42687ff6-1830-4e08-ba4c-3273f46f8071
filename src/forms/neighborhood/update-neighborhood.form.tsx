import FormField from '@/components/forms/FormField'
import FormLocation, { locationSchema } from '@/components/forms/FormLocation'
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload'
import { FormComboBox } from '@/components/forms/FormComboBox'
import { FormSlug, sanitizeSlug } from '@/components/forms/FormSlug'

import { Button } from '@/components/ui/button'
import {
  useUpdateNeighborhoodMutation,
  useNeighborhoodsQuery,
  useCitiesQuery,
} from '@/generated/graphql'
import { toastPromise } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import type { SubmitHandler, UseFormReturn } from 'react-hook-form'
import { FormProvider, useForm } from 'react-hook-form'
import { z } from 'zod'
import { useEffect } from 'react'

const schema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Name is required'),
  slug: z.string().min(1, 'Slug is required'),
  city: z.string().min(1, 'City is required'),
  coverImage: z.string().optional(),
  images: z.array(z.string()).optional(),
  location: locationSchema,
})

type UpdateNeighborhoodForm = z.infer<typeof schema>

type UpdateNeighborhoodFormProps = {
  methods: UseFormReturn<UpdateNeighborhoodForm>
  onSubmit: SubmitHandler<UpdateNeighborhoodForm>
  isLoading?: boolean
}

export const useUpdateNeighborhoodFormMethods = (neighborhoodName: string) => {
  const { mutateAsync: updateNeighborhood } = useUpdateNeighborhoodMutation()
  
  // Fetch neighborhood data using the neighborhoods query with name filter
  const { data: neighborhoodsData, isLoading } = useNeighborhoodsQuery({
    neighborhoodsInput: { name: neighborhoodName },
    paginationInput: { limit: 1, page: 1 },
  }, {
    initialData: { neighborhoods: { docs: [], totalDocs: 0, limit: 1, page: 1, totalPages: 0, prevPage: null, nextPage: null, hasPrevPage: false, hasNextPage: false, pagingCounter: 1 } }
  })

  const methods = useForm<UpdateNeighborhoodForm>({
    defaultValues: {
      id: '',
      name: '',
      slug: '',
      city: '',
      coverImage: '',
      images: [],
      location: {
        center: [48.8566, 2.3522],
      },
    },
    resolver: zodResolver(schema),
  })

  // Populate form when neighborhood data is loaded
  useEffect(() => {
    if (neighborhoodsData?.neighborhoods.docs.length > 0) {
      const neighborhood = neighborhoodsData.neighborhoods.docs[0]
      methods.reset({
        id: neighborhood.id,
        name: neighborhood.name,
        slug: neighborhood.slug,
        city: neighborhood.city.id,
        coverImage: neighborhood.coverImage || '',
        images: neighborhood.images || [],
        location: neighborhood.location,
      })
    }
  }, [neighborhoodsData, methods])

  const onSubmit: SubmitHandler<UpdateNeighborhoodForm> = (data) => {
    toastPromise({
      asyncFunc: updateNeighborhood({ input: data }),
      success: 'Neighborhood updated successfully',
      error: 'Failed to update neighborhood',
      onSuccess() {
        // Optionally redirect or refresh data
      },
      onError(err) {
        console.error(err)
      },
    })
  }

  return { methods, onSubmit, isLoading }
}

export default function UpdateNeighborhoodForm({
  methods,
  onSubmit,
  isLoading = false,
}: UpdateNeighborhoodFormProps) {
  // Fetch cities for the dropdown
  const { data: citiesData, isLoading: citiesLoading } = useCitiesQuery({
    paginationInput: { limit: 100, page: 1 }, // Get all cities for dropdown
  })

  // Transform cities data for the select component
  const cityOptions =
    citiesData?.cities.docs.map((city) => ({
      value: city.id,
      label: city.name,
    })) || []

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className="w-full grid grid-cols-2 gap-5 p-10"
      >
        <div className="space-y-4">
          <FormField
            name="name"
            label="Name"
            placeholder="Enter neighborhood name"
            onChange={(e) => {
              methods.setValue('name', e.target.value)
              const slug = sanitizeSlug(e.target.value)
              methods.setValue('slug', slug)
            }}
          />

          <FormSlug
            name="slug"
            label="Slug"
            placeholder="Enter neighborhood slug"
          />

          <FormComboBox
            name="city"
            label="City"
            placeholder="Select a city"
            options={cityOptions}
            disabled={citiesLoading}
          />

          <FormPresignedUpload
            name="coverImage"
            label="Cover Image"
            accept="image/*"
          />

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? 'Loading...' : 'Update Neighborhood'}
          </Button>
        </div>

        {/* Form */}
        <div>
          <FormLocation
            label="Location"
            name="location"
            placeholder="Search location"
          />
        </div>
      </form>
    </FormProvider>
  )
}
