import FormField from '@/components/forms/FormField'
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload'
import { FormTextField } from '@/components/forms/FormTextField'
import { Button } from '@/components/ui/button'
import { useCreateBarCategoryMutation } from '@/generated/graphql'
import { toastPromise } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  FormProvider,
  useForm,
  type SubmitHandler,
  type UseFormReturn,
} from 'react-hook-form'
import * as z from 'zod'

/** Bar Category Schema */
export const BarCategorySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  image: z.string().optional(),
  icon: z.string().optional(),
})

export type CreateBarCategoryForm = z.infer<typeof BarCategorySchema>

export type CreateBarCategoryFormProps = {
  methods: UseFormReturn<CreateBarCategoryForm>
  onSubmit: SubmitHandler<CreateBarCategoryForm>
}

export const useCreateBarCategoryFormMethods = () => {
  const { mutateAsync: createBarCategory } = useCreateBarCategoryMutation()
  const methods = useForm<CreateBarCategoryForm>({
    defaultValues: {
      name: '',
      description: '',
      image: '',
      icon: '',
    },
    resolver: zodResolver(BarCategorySchema),
  })

  const onSubmit: SubmitHandler<CreateBarCategoryForm> = (data) => {
    // Filter out empty optional fields
    const cleanedData = {
      name: data.name,
      ...(data.description && { description: data.description }),
      ...(data.image && { image: data.image }),
      ...(data.icon && { icon: data.icon }),
    }

    toastPromise({
      asyncFunc: createBarCategory({ input: cleanedData }),
      success: 'Bar category created successfully',
      error: 'Failed to create bar category',
      onSuccess() {
        methods.reset()
      },
      onError(err) {
        console.error(err)
      },
    })
  }

  return { methods, onSubmit }
}

export default function CreateBarCategoryForm({
  methods,
  onSubmit,
}: CreateBarCategoryFormProps) {
  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className="w-full max-w-2xl mx-auto space-y-8 p-6"
      >
        {/* Header */}
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-bold">Create Bar Category</h2>
          <p className="text-muted-foreground">
            Add a new category to organize bars in your platform
          </p>
        </div>

        {/* Basic Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Basic Information</h3>

          <FormField
            name="name"
            label="Category Name"
            placeholder="e.g., Sports Bar, Cocktail Lounge, Wine Bar"
            description="Enter a descriptive name for this bar category"
          />

          <FormTextField
            name="description"
            label="Description (Optional)"
            placeholder="Describe what types of bars belong to this category..."
            description="Provide additional details about this category"
            multiline
            rows={3}
          />
        </div>

        {/* Visual Elements Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Visual Elements</h3>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <FormPresignedUpload
                name="image"
                label="Category Image (Optional)"
                accept="image/*"
              />
              <p className="text-sm text-muted-foreground">
                Upload an image to represent this category
              </p>
            </div>

            <FormField
              name="icon"
              label="Lucide Icon Name (Optional)"
              placeholder="e.g., Wine, Coffee, Music, Gamepad2"
              description="Name of a Lucide React icon (without 'Icon' suffix)"
            />
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end pt-6">
          <Button type="submit" size="lg" className="min-w-32">
            Create Category
          </Button>
        </div>
      </form>
    </FormProvider>
  )
}
