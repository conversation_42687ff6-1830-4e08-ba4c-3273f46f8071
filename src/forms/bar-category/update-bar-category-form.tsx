import FormField from '@/components/forms/FormField'
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload'
import { FormTextField } from '@/components/forms/FormTextField'
import { Button } from '@/components/ui/button'
import {
  useUpdateBarCategoryMutation,
  useBarCategoriesQuery,
} from '@/generated/graphql'
import { toastPromise } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import type { SubmitHandler, UseFormReturn } from 'react-hook-form'
import { FormProvider, useForm } from 'react-hook-form'
import { z } from 'zod'
import { useEffect } from 'react'

// Import the existing bar category schema from create form
import { BarCategorySchema } from './create-bar-category-form'

// Update schema to include id field
const UpdateBarCategorySchema = BarCategorySchema.extend({
  id: z.string(),
})

type UpdateBarCategoryForm = z.infer<typeof UpdateBarCategorySchema>

type UpdateBarCategoryFormProps = {
  methods: UseFormReturn<UpdateBarCategoryForm>
  onSubmit: SubmitHandler<UpdateBarCategoryForm>
  isLoading?: boolean
}

export const useUpdateBarCategoryFormMethods = (categoryName: string) => {
  const { mutateAsync: updateBarCategory } = useUpdateBarCategoryMutation()

  // Fetch bar category data using the barCategories query with name filter
  const { data: barCategoriesData, isLoading } = useBarCategoriesQuery(
    {
      barCategoriesInput: { name: categoryName },
      paginationInput: { limit: 1, page: 1 },
    },
    {
      initialData: {
        barCategories: {
          docs: [],
          totalDocs: 0,
          limit: 1,
          page: 1,
          totalPages: 0,
          prevPage: null,
          nextPage: null,
          hasPrevPage: false,
          hasNextPage: false,
          pagingCounter: 1,
        },
      },
    },
  )

  const methods = useForm<UpdateBarCategoryForm>({
    defaultValues: {
      id: '',
      name: '',
      description: '',
      image: '',
      icon: '',
    },
    resolver: zodResolver(UpdateBarCategorySchema),
  })

  // Populate form when bar category data is loaded
  useEffect(() => {
    if (barCategoriesData?.barCategories?.docs && barCategoriesData.barCategories.docs.length > 0) {
      const category = barCategoriesData.barCategories.docs[0]
      methods.reset({
        id: category.id,
        name: category.name,
        description: category.description || '',
        image: category.image || '',
        icon: category.icon || '',
      })
    }
  }, [barCategoriesData, methods])

  const onSubmit: SubmitHandler<UpdateBarCategoryForm> = (data) => {
    // Filter out empty optional fields
    const cleanedData = {
      id: data.id,
      ...(data.name && { name: data.name }),
      ...(data.description && { description: data.description }),
      ...(data.image && { image: data.image }),
      ...(data.icon && { icon: data.icon }),
    }

    toastPromise({
      asyncFunc: updateBarCategory({ input: cleanedData }),
      success: 'Bar category updated successfully',
      error: 'Failed to update bar category',
      onSuccess() {
        // Optionally redirect or refresh data
      },
      onError(err) {
        console.error(err)
      },
    })
  }

  return { methods, onSubmit, isLoading }
}

export default function UpdateBarCategoryForm({
  methods,
  onSubmit,
  isLoading = false,
}: UpdateBarCategoryFormProps) {
  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className="w-full max-w-2xl mx-auto space-y-8 p-6"
      >
        {/* Header */}
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-bold">Update Bar Category</h2>
          <p className="text-muted-foreground">
            Modify the details of this bar category
          </p>
        </div>

        {/* Basic Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Basic Information</h3>

          <FormField
            name="name"
            label="Category Name"
            placeholder="e.g., Sports Bar, Cocktail Lounge, Wine Bar"
            description="Enter a descriptive name for this bar category"
          />

          <FormTextField
            name="description"
            label="Description (Optional)"
            placeholder="Describe what types of bars belong to this category..."
            description="Provide additional details about this category"
            multiline
            rows={3}
          />
        </div>

        {/* Visual Elements Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Visual Elements</h3>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <FormPresignedUpload
                name="image"
                label="Category Image (Optional)"
                accept="image/*"
              />
              <p className="text-sm text-muted-foreground">
                Upload an image to represent this category
              </p>
            </div>

            <FormField
              name="icon"
              label="Lucide Icon Name (Optional)"
              placeholder="e.g., Wine, Coffee, Music, Gamepad2"
              description="Name of a Lucide React icon (without 'Icon' suffix)"
            />
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end pt-6">
          <Button type="submit" size="lg" className="min-w-32" disabled={isLoading}>
            {isLoading ? 'Updating...' : 'Update Category'}
          </Button>
        </div>
      </form>
    </FormProvider>
  )
}
