import { Button } from '@/components/ui/button'
import {
  useUpdateBarMutation,
  useBarsQuery,
  useCitiesQuery,
  useNeighborhoodsQuery,
  useBarCategoriesQuery,
  BarStatus,
  BarMenuItemAvailability,
  DayOfWeek,
} from '@/generated/graphql'
import { toastPromise } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import type { SubmitHandler, UseFormReturn } from 'react-hook-form'
import { FormProvider, useForm } from 'react-hook-form'
import { useEffect } from 'react'

// Import the existing bar schema and components from create form
import CreateBarForm, { BarSchema } from './create-bar-form'
import { z } from 'zod'

// Update schema to include id field
const UpdateBarSchema = BarSchema.extend({
  id: z.string(),
})

type UpdateBarForm = z.infer<typeof UpdateBarSchema>

type UpdateBarFormProps = {
  methods: UseFormReturn<UpdateBarForm>
  onSubmit: SubmitHandler<UpdateBarForm>
  isLoading?: boolean
}

export const useUpdateBarFormMethods = (barSlug: string) => {
  const { mutateAsync: updateBar } = useUpdateBarMutation()

  // Fetch bar data using the bars query with slug filter
  const { data: barsData, isLoading } = useBarsQuery(
    {
      barsInput: { slug: barSlug },
      paginationInput: { limit: 1, page: 1 },
    },
    {
      initialData: {
        bars: {
          docs: [],
          totalDocs: 0,
          limit: 1,
          page: 1,
          totalPages: 0,
          prevPage: null,
          nextPage: null,
          hasPrevPage: false,
          hasNextPage: false,
          pagingCounter: 1,
        },
      },
    },
  )

  const methods = useForm<UpdateBarForm>({
    defaultValues: {
      id: '',
      address: {
        address: '',
        location: {
          center: [48.8566, 2.3522],
        },
        metroLine: '',
        metroStation: '',
      },
      businessHours: {
        schedule: [
          { day: DayOfWeek.Monday, timings: [1700, 2300] as [number, number] },
          { day: DayOfWeek.Tuesday, timings: [1700, 2300] as [number, number] },
          {
            day: DayOfWeek.Wednesday,
            timings: [1700, 2300] as [number, number],
          },
          {
            day: DayOfWeek.Thursday,
            timings: [1700, 2359] as [number, number],
          },
          { day: DayOfWeek.Friday, timings: [1700, 2359] as [number, number] },
          {
            day: DayOfWeek.Saturday,
            timings: [1400, 2359] as [number, number],
          },
          { day: DayOfWeek.Sunday, timings: [1400, 2300] as [number, number] },
        ],
      },
      happyHours: {
        schedule: [
          { day: DayOfWeek.Monday, timings: [1700, 1900] as [number, number] },
          { day: DayOfWeek.Tuesday, timings: [1700, 1900] as [number, number] },
          {
            day: DayOfWeek.Wednesday,
            timings: [1700, 1900] as [number, number],
          },
          {
            day: DayOfWeek.Thursday,
            timings: [1700, 1900] as [number, number],
          },
          { day: DayOfWeek.Friday, timings: [1700, 1900] as [number, number] },
          {
            day: DayOfWeek.Saturday,
            timings: [1400, 1600] as [number, number],
          },
          { day: DayOfWeek.Sunday, timings: [1400, 1600] as [number, number] },
        ],
      },
      categories: [],
      city: '',
      neighborhood: '',
      coverImage: '',
      description: '',
      featured: false,
      images: [],
      logo: '',
      menu: {
        currency: '€',
        sections: [
          {
            name: 'Cocktails',
            items: [
              {
                name: '',
                price: 0,
                available: BarMenuItemAvailability.Available,
              },
            ],
          },
        ],
      },
      name: '',
      contact: {
        countryCode: '+33',
        phone: '',
      },
      rating: 0,
      slug: '',
      status: BarStatus.Active,
      location: {
        center: [48.8566, 2.3522],
      },
    },
    resolver: zodResolver(UpdateBarSchema),
  })

  // Update form when bar data is loaded
  useEffect(() => {
    if (barsData?.bars?.docs?.[0]) {
      const bar = barsData.bars.docs[0]

      // Reset form with bar data
      methods.reset({
        id: bar.id,
        name: bar.name,
        slug: bar.slug,
        description: bar.description || '',
        categories: bar.categories?.map((cat) => cat.id) || [],
        city: bar.city?.id || '',
        neighborhood: bar.neighborhood?.id || '',
        status: bar.status,
        logo: bar.logo || '',
        coverImage: bar.coverImage || '',
        images: bar.images || [],
        contact: bar.phone || { countryCode: '+33', phone: '' },
        address: bar.address || {
          address: '',
          location: { center: [48.8566, 2.3522] },
          metroLine: '',
          metroStation: '',
        },
        businessHours: bar.businessHours || {
          schedule: [
            {
              day: DayOfWeek.Monday,
              timings: [1700, 2300] as [number, number],
            },
            {
              day: DayOfWeek.Tuesday,
              timings: [1700, 2300] as [number, number],
            },
            {
              day: DayOfWeek.Wednesday,
              timings: [1700, 2300] as [number, number],
            },
            {
              day: DayOfWeek.Thursday,
              timings: [1700, 2359] as [number, number],
            },
            {
              day: DayOfWeek.Friday,
              timings: [1700, 2359] as [number, number],
            },
            {
              day: DayOfWeek.Saturday,
              timings: [1400, 2359] as [number, number],
            },
            {
              day: DayOfWeek.Sunday,
              timings: [1400, 2300] as [number, number],
            },
          ],
        },
        happyHours: bar.happyHours || {
          schedule: [
            {
              day: DayOfWeek.Monday,
              timings: [1700, 1900] as [number, number],
            },
            {
              day: DayOfWeek.Tuesday,
              timings: [1700, 1900] as [number, number],
            },
            {
              day: DayOfWeek.Wednesday,
              timings: [1700, 1900] as [number, number],
            },
            {
              day: DayOfWeek.Thursday,
              timings: [1700, 1900] as [number, number],
            },
            {
              day: DayOfWeek.Friday,
              timings: [1700, 1900] as [number, number],
            },
            {
              day: DayOfWeek.Saturday,
              timings: [1400, 1600] as [number, number],
            },
            {
              day: DayOfWeek.Sunday,
              timings: [1400, 1600] as [number, number],
            },
          ],
        },
        menu: bar.menu || {
          currency: '€',
          sections: [
            {
              name: 'Cocktails',
              items: [
                {
                  name: '',
                  price: 0,
                  available: BarMenuItemAvailability.Available,
                },
              ],
            },
          ],
        },
        featured: bar.featured || false,
        rating: bar.rating || 0,
        location: bar.location || { center: [48.8566, 2.3522] },
      })
    }
  }, [barsData, methods])

  const onSubmit: SubmitHandler<UpdateBarForm> = (data) => {
    toastPromise({
      asyncFunc: updateBar({ input: data }),
      success: 'Bar updated successfully',
      error: 'Failed to update bar',
      onSuccess() {
        // Optionally redirect or show success message
      },
      onError(err) {
        console.error(err)
      },
    })
  }

  return { methods, onSubmit, isLoading }
}

export default function UpdateBarForm({
  methods,
  onSubmit,
  isLoading,
}: UpdateBarFormProps) {
  if (isLoading) {
    return <div>Loading bar data...</div>
  }

  // Reuse the CreateBarForm component but with different submit text
  return (
    <CreateBarForm
      methods={methods}
      onSubmit={onSubmit}
      submitButtonText="Update Bar"
    />
  )
}
