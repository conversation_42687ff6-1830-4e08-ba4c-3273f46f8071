import { FormBusinessHours } from '@/components/forms/FormBusinessHours'
import { FormComboBox } from '@/components/forms/FormComboBox'
import { FormComboBoxPopover } from '@/components/forms/FormComboPopover'
import FormField from '@/components/forms/FormField'
import FormLocation, { locationSchema } from '@/components/forms/FormLocation'
import { FormPhoneInput, phoneSchema } from '@/components/forms/FormPhoneInput'
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload'
import { FormSelect } from '@/components/forms/FormSelect'
import { FormSlider } from '@/components/forms/FormSlider'
import { FormSlug, sanitizeSlug } from '@/components/forms/FormSlug'
import { FormSwitch } from '@/components/forms/FormSwitch'
import { FormTextField } from '@/components/forms/FormTextField'
import { Button } from '@/components/ui/button'
import {
  BarMenuItemAvailability,
  BarStatus,
  DayOfWeek,
  useBarCategoriesQuery,
  useCitiesQuery,
  useCreateBarMutation,
  useNeighborhoodsQuery,
} from '@/generated/graphql'
import { toastPromise } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import { Plus, Trash2 } from 'lucide-react'
import { useMemo } from 'react'
import {
  FormProvider,
  useFieldArray,
  useForm,
  type SubmitHandler,
  type UseFormReturn,
} from 'react-hook-form'
import * as z from 'zod'

export const DayTimingSchema = z.object({
  day: z.nativeEnum(DayOfWeek),
  timings: z
    .tuple([z.coerce.number(), z.coerce.number()])
    .refine(([openTime, closeTime]) => {
      // Validate HHMM format for both times
      const isValidOpen =
        openTime >= 0 &&
        openTime <= 2359 &&
        Math.floor(openTime / 100) <= 23 &&
        openTime % 100 <= 59
      const isValidClose =
        closeTime >= 0 &&
        closeTime <= 2359 &&
        Math.floor(closeTime / 100) <= 23 &&
        closeTime % 100 <= 59
      return isValidOpen && isValidClose
    }, 'Both opening and closing times must be valid HHMM format (0000-2359)'),
})

/** BusinessHoursSchedule */
export const BusinessHoursScheduleSchema = z.object({
  schedule: z.array(DayTimingSchema),
})

/** HappyHoursSchedule */
export const HappyHoursScheduleSchema = z.object({
  schedule: z.array(DayTimingSchema),
})

/** MenuItem */
export const MenuItemSchema = z.object({
  name: z.string().min(1, 'Item name is required'),
  price: z.coerce.number().min(0, 'Price must be 0 or greater'),
  available: z.nativeEnum(BarMenuItemAvailability),
})

/** MenuSection */
export const MenuSectionSchema = z.object({
  name: z.string().min(1, 'Section name is required'),
  items: z.array(MenuItemSchema),
})

/** Menu */
export const MenuSchema = z.object({
  currency: z.string(),
  sections: z.array(MenuSectionSchema),
})

/** Contact Schema - using the exported phoneSchema */
export const ContactSchema = phoneSchema

/** Address Schema - using the imported locationSchema */
export const AddressSchema = z.object({
  address: z.string().min(1, 'Address is required'),
  location: locationSchema,
  metroLine: z.string().optional(),
  metroStation: z.string().optional(),
})

/** Bar Schema */
export const BarSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  slug: z.string().min(1, 'Slug is required'),
  description: z.string().min(1, 'Description is required'),
  categories: z.array(z.string()).min(1, 'At least one category is required'),
  city: z.string().min(1, 'City is required'),
  neighborhood: z.string().optional(),
  status: z.nativeEnum(BarStatus),
  logo: z.string().optional(),
  coverImage: z.string().optional(),
  images: z.array(z.string()),
  contact: ContactSchema.optional(),
  address: AddressSchema.optional(),
  businessHours: BusinessHoursScheduleSchema.optional(),
  happyHours: HappyHoursScheduleSchema.optional(),
  menu: MenuSchema.optional(),
  featured: z.boolean(),
  rating: z.coerce.number().min(0).max(5),
  location: locationSchema,
})

export type CreateBarForm = z.infer<typeof BarSchema>

export type CreateBarFormProps = {
  methods: UseFormReturn<CreateBarForm>
  onSubmit: SubmitHandler<CreateBarForm>
  submitButtonText?: string
}

// Component for managing individual menu sections
function MenuSectionComponent({
  sectionIndex,
  methods,
  onRemoveSection,
}: {
  sectionIndex: number
  methods: UseFormReturn<CreateBarForm>
  onRemoveSection: () => void
}) {
  const {
    append: menuItemAppend,
    remove: menuItemRemove,
    fields: menuItemFields,
  } = useFieldArray({
    control: methods.control,
    name: `menu.sections.${sectionIndex}.items`,
  })

  return (
    <div className="border rounded-lg p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h5 className="text-sm font-medium">Menu Section {sectionIndex + 1}</h5>
        <Button
          onClick={onRemoveSection}
          type="button"
          variant="destructive"
          size="sm"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      {/* Section Name */}
      <FormField
        name={`menu.sections.${sectionIndex}.name`}
        label="Section Name"
        placeholder="e.g., Cocktails, Beer, Wine, Food"
      />

      {/* Menu Items in this section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h6 className="text-xs font-medium text-muted-foreground">
            Items in this section
          </h6>
          <Button
            onClick={() =>
              menuItemAppend({
                available: BarMenuItemAvailability.Available,
                name: '',
                price: 0,
              })
            }
            type="button"
            variant="outline"
            size="sm"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Item
          </Button>
        </div>

        {menuItemFields.map((item, itemIdx) => (
          <div key={item.id} className="border-l-2 border-muted pl-4 space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">
                Item {itemIdx + 1}
              </span>
              <Button
                onClick={() => menuItemRemove(itemIdx)}
                type="button"
                variant="ghost"
                size="sm"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <FormField
                name={`menu.sections.${sectionIndex}.items.${itemIdx}.name`}
                label="Item Name"
                placeholder="e.g., Mojito, Beer, Wine"
              />
              <FormField
                name={`menu.sections.${sectionIndex}.items.${itemIdx}.price`}
                label="Price"
                placeholder="0.00"
                type="number"
                step="0.01"
              />
              <FormSelect
                name={`menu.sections.${sectionIndex}.items.${itemIdx}.available`}
                label="Availability"
                options={[
                  {
                    label: 'Available',
                    value: BarMenuItemAvailability.Available,
                  },
                  {
                    label: 'Unavailable',
                    value: BarMenuItemAvailability.Unavailable,
                  },
                ]}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export const useCreateBarFormMethods = () => {
  const { mutateAsync: createBar } = useCreateBarMutation()
  const methods = useForm<CreateBarForm>({
    defaultValues: {
      address: {
        address: '',
        location: {
          center: [48.8566, 2.3522], // Default to Paris coordinates
        },
        metroLine: '',
        metroStation: '',
      },
      businessHours: {
        schedule: [
          {
            day: DayOfWeek.Monday,
            timings: [1700, 2300] as [number, number], // 5:00 PM to 11:00 PM
          },
          {
            day: DayOfWeek.Tuesday,
            timings: [1700, 2300] as [number, number],
          },
          {
            day: DayOfWeek.Wednesday,
            timings: [1700, 2300] as [number, number],
          },
          {
            day: DayOfWeek.Thursday,
            timings: [1700, 2359] as [number, number], // 5:00 PM to 11:59 PM
          },
          {
            day: DayOfWeek.Friday,
            timings: [1700, 2359] as [number, number], // 5:00 PM to 11:59 PM
          },
          {
            day: DayOfWeek.Saturday,
            timings: [1400, 2359] as [number, number], // 2:00 PM to 11:59 PM
          },
          {
            day: DayOfWeek.Sunday,
            timings: [1400, 2300] as [number, number], // 2:00 PM to 11:00 PM
          },
        ],
      },
      happyHours: {
        schedule: [
          {
            day: DayOfWeek.Monday,
            timings: [1700, 1900] as [number, number], // 5:00 PM to 7:00 PM
          },
          {
            day: DayOfWeek.Tuesday,
            timings: [1700, 1900] as [number, number],
          },
          {
            day: DayOfWeek.Wednesday,
            timings: [1700, 1900] as [number, number],
          },
          {
            day: DayOfWeek.Thursday,
            timings: [1700, 1900] as [number, number],
          },
          {
            day: DayOfWeek.Friday,
            timings: [1700, 1900] as [number, number],
          },
          {
            day: DayOfWeek.Saturday,
            timings: [1400, 1600] as [number, number], // 2:00 PM to 4:00 PM
          },
          {
            day: DayOfWeek.Sunday,
            timings: [1400, 1600] as [number, number],
          },
        ],
      },
      categories: [],
      city: '',
      neighborhood: '',
      coverImage: '',
      description: '',
      featured: false,
      images: [],
      logo: '',
      menu: {
        currency: '€',
        sections: [
          {
            name: 'Cocktails',
            items: [
              {
                name: '',
                price: 0,
                available: BarMenuItemAvailability.Available,
              },
            ],
          },
        ],
      },
      name: '',
      contact: {
        countryCode: '+33',
        phone: '',
      },
      rating: 0,
      slug: '',
      status: BarStatus.Active,
    },
    resolver: zodResolver(BarSchema),
  })

  const onSubmit: SubmitHandler<CreateBarForm> = (data) => {
    toastPromise({
      asyncFunc: createBar({ input: data }),
      success: 'Bar created successfully',
      error: 'Failed to create bar',
      onSuccess() {
        methods.reset()
      },
      onError(err) {
        console.error(err)
      },
    })
  }

  return { methods, onSubmit }
}

export default function CreateBarForm({
  methods,
  onSubmit,
  submitButtonText = 'Create Bar',
}: CreateBarFormProps) {
  // Fetch cities for city selection
  const { data: citiesData, isLoading: citiesLoading } = useCitiesQuery({
    paginationInput: { limit: 100, page: 1 },
  })

  // Fetch neighborhoods for neighborhood selection
  const { data: neighborhoodsData, isLoading: neighborhoodsLoading } =
    useNeighborhoodsQuery({
      paginationInput: { limit: 100, page: 1 },
    })

  // Fetch bar categories for category selection
  const { data: barCategoriesData, isLoading: barCategoriesLoading } =
    useBarCategoriesQuery({
      paginationInput: { limit: 100, page: 1 },
    })

  // Watch city value to filter neighborhoods
  const selectedCity = methods.watch('city')

  // Prepare city options
  const cityOptions = useMemo(() => {
    if (!citiesData?.cities?.docs) return []
    return citiesData.cities.docs.map((city) => ({
      label: city.name,
      value: city.id,
    }))
  }, [citiesData])

  // Prepare neighborhood options (filtered by selected city)
  const neighborhoodOptions = useMemo(() => {
    if (!neighborhoodsData?.neighborhoods?.docs) return []
    return neighborhoodsData.neighborhoods.docs
      .filter(
        (neighborhood) =>
          !selectedCity || neighborhood.city.id === selectedCity,
      )
      .map((neighborhood) => ({
        label: neighborhood.name,
        value: neighborhood.id,
      }))
  }, [neighborhoodsData, selectedCity])

  // Prepare bar category options
  const barCategoryOptions = useMemo(() => {
    if (!barCategoriesData?.barCategories?.docs) return []
    return barCategoriesData.barCategories.docs.map((category) => ({
      label: category.name,
      value: category.id,
    }))
  }, [barCategoriesData])

  // Menu sections field array
  const {
    append: menuSectionAppend,
    remove: menuSectionRemove,
    fields: menuSectionFields,
  } = useFieldArray({
    control: methods.control,
    name: 'menu.sections',
  })

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className="w-full grid grid-cols-2 gap-5 p-10"
      >
        {/* Main Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Basic Information</h3>

          <FormField
            name="name"
            label="Name"
            placeholder="Enter bar name"
            onChange={(e) => {
              methods.setValue('name', e.target.value)
              const slug = sanitizeSlug(e.target.value)
              methods.setValue('slug', slug)
            }}
          />

          <FormSlug name="slug" label="Slug" placeholder="Enter bar slug" />

          <FormTextField
            name="description"
            label="Description"
            placeholder="Enter bar description"
          />

          <div className="grid grid-cols-2 gap-4">
            <FormComboBox
              name="city"
              label="City"
              placeholder="Select a city"
              options={cityOptions}
              disabled={citiesLoading}
            />

            <FormComboBox
              name="neighborhood"
              label="Neighborhood (Optional)"
              placeholder="Select a neighborhood"
              options={neighborhoodOptions}
              disabled={neighborhoodsLoading}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <FormSlider
              name="rating"
              label="Rating"
              min={0}
              max={5}
              step={0.1}
            />

            <FormSwitch
              name="featured"
              label="Featured"
              description="Mark this bar as featured"
            />
          </div>

          <FormSelect
            name="status"
            label="Status"
            options={[
              { label: 'Active', value: BarStatus.Active },
              { label: 'Inactive', value: BarStatus.Inactive },
              { label: 'Pending', value: BarStatus.Pending },
              { label: 'Suspended', value: BarStatus.Suspended },
            ]}
          />
        </div>

        {/* Media and Contact Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Media & Contact</h3>

          <FormPresignedUpload name="logo" label="Logo" accept="image/*" />

          <FormPresignedUpload
            name="coverImage"
            label="Cover Image"
            accept="image/*"
          />

          <FormPresignedUpload
            name="images"
            label="Additional Images"
            accept="image/*"
            multiple
          />

          <FormPhoneInput
            name="contact"
            label="Phone Number"
            placeholder="Enter phone number"
          />
        </div>

        {/* Address Section */}
        <div className="col-span-2 space-y-4">
          <h3 className="text-lg font-medium">Address & Location</h3>

          <div className="grid grid-cols-3 gap-4">
            <FormField
              name="address.address"
              label="Street Address"
              placeholder="Enter street address"
            />

            <FormField
              name="address.metroLine"
              label="Metro Line (Optional)"
              placeholder="e.g., Line 1, RER A"
            />

            <FormField
              name="address.metroStation"
              label="Metro Station (Optional)"
              placeholder="e.g., Châtelet, Gare du Nord"
            />
          </div>

          <FormLocation
            name="location"
            label="Location"
            description="Click on the map to set the bar's location"
          />
        </div>

        {/* Categories Section */}
        <div className="col-span-2 space-y-4">
          <h3 className="text-lg font-medium">Categories</h3>

          <FormComboBoxPopover
            name="categories"
            label="Bar Categories"
            placeholder="Select categories"
            options={barCategoryOptions}
            disabled={barCategoriesLoading}
            multiple
          />
        </div>

        {/* Menu Section */}
        <div className="col-span-2 space-y-4">
          <h3 className="text-lg font-medium">Menu</h3>

          <FormField
            name="menu.currency"
            label="Currency"
            placeholder="e.g., €, $, £"
          />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-md font-medium">Menu Sections</h4>
              <Button
                onClick={() =>
                  menuSectionAppend({
                    name: '',
                    items: [
                      {
                        available: BarMenuItemAvailability.Available,
                        name: '',
                        price: 0,
                      },
                    ],
                  })
                }
                type="button"
                variant="outline"
                size="sm"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Menu Section
              </Button>
            </div>

            {menuSectionFields.map((section, sectionIdx) => (
              <MenuSectionComponent
                key={section.id}
                sectionIndex={sectionIdx}
                methods={methods}
                onRemoveSection={() => menuSectionRemove(sectionIdx)}
              />
            ))}
          </div>
        </div>

        {/* Business Hours Section */}
        <FormBusinessHours
          name="businessHours"
          label="Business Hours"
          description="Set opening and closing times for each day of the week"
        />

        {/* Happy Hours Section */}
        <FormBusinessHours
          name="happyHours"
          label="Happy Hours"
          description="Set happy hour times for each day of the week"
        />

        {/* Submit Button */}
        <div className="col-span-2">
          <Button type="submit" className="w-full">
            {submitButtonText}
          </Button>
        </div>
      </form>
    </FormProvider>
  )
}
