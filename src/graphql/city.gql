fragment CitiesPaginationInfo on PaginatedCities {
  totalDocs
  limit
  page
  totalPages
  prevPage
  nextPage
  hasPrevPage
  hasNextPage
  pagingCounter
}

mutation CreateCity($input: CreateCityInput!) {
  createCity(createCityInput: $input) {
    _id
  }
}

mutation UpdateCity($input: UpdateCityInput!) {
  updateCity(updateCityInput: $input) {
    _id
  }
}

query Cities($citiesInput: CitiesInput, $paginationInput: PaginationInput) {
  cities(citiesInput: $citiesInput, paginationInput: $paginationInput) {
    ...CitiesPaginationInfo
    docs {
      id
      name
      image
      coverImage
      heading
      subHeading
      location {
        center
      }
    }
  }
}
