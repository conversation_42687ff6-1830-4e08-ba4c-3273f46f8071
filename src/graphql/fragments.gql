# Pagination fragments for different entity types

# Define a single fragment with all pagination fields
fragment UsersPaginationInfo on PaginatedUsers {
  totalDocs
  limit
  page
  totalPages
  prevPage
  nextPage
  hasPrevPage
  hasNextPage
  pagingCounter
}

fragment CitiesPaginationInfo on PaginatedCities {
  totalDocs
  limit
  page
  totalPages
  prevPage
  nextPage
  hasPrevPage
  hasNextPage
  pagingCounter
}

fragment ClubsPaginationInfo on PaginatedClubs {
  totalDocs
  limit
  page
  totalPages
  prevPage
  nextPage
  hasPrevPage
  hasNextPage
  pagingCounter
}

fragment NeighborhoodsPaginationInfo on PaginatedNeighborhoods {
  totalDocs
  limit
  page
  totalPages
  prevPage
  nextPage
  hasPrevPage
  hasNextPage
  pagingCounter
}

fragment ClubCategoriesPaginationInfo on PaginatedClubCategories {
  totalDocs
  limit
  page
  totalPages
  prevPage
  nextPage
  hasPrevPage
  hasNextPage
  pagingCounter
}

fragment BarsPaginationInfo on PaginatedBars {
  totalDocs
  limit
  page
  totalPages
  prevPage
  nextPage
  hasPrevPage
  hasNextPage
  pagingCounter
}

fragment BarCategoriesPaginationInfo on PaginatedBarCategories {
  totalDocs
  limit
  page
  totalPages
  prevPage
  nextPage
  hasPrevPage
  hasNextPage
  pagingCounter
}
