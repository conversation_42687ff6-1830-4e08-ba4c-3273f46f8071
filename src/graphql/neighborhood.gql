# import "./fragments.gql"

query Neighborhoods(
  $neighborhoodsInput: NeighborhoodsInput
  $paginationInput: PaginationInput
) {
  neighborhoods(
    neighborhoodsInput: $neighborhoodsInput
    paginationInput: $paginationInput
  ) {
    ...NeighborhoodsPaginationInfo
    docs {
      id
      name
      slug
      coverImage
      images
      location {
        center
      }
      city {
        id
        name
      }
    }
  }
}

mutation CreateNeighborhood($input: CreateNeighborhoodInput!) {
  createNeighborhood(createNeighborhoodInput: $input) {
    _id
  }
}

mutation UpdateNeighborhood($input: UpdateNeighborhoodInput!) {
  updateNeighborhood(updateNeighborhoodInput: $input) {
    _id
  }
}
