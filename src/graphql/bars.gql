# import "./fragments.gql"

query Bars($barsInput: BarsInput, $paginationInput: PaginationInput) {
  bars(barsInput: $barsInput, paginationInput: $paginationInput) {
    ...BarsPaginationInfo
    docs {
      id
      name
      slug
      coverImage
      images
      city {
        id
        name
      }
      businessHours {
        schedule {
          day
          timings
        }
      }
      happyHours {
        schedule {
          day
          timings
        }
      }
      neighborhood {
        id
        name
      }
    }
  }
}

mutation CreateBar($input: CreateBarInput!) {
  createBar(createBarInput: $input) {
    id
  }
}

mutation UpdateBar($input: UpdateBarInput!) {
  updateBar(updateBarInput: $input) {
    id
  }
}

query BarCategories(
  $paginationInput: PaginationInput
  $barCategoriesInput: BarCategoriesInput
) {
  barCategories(
    paginationInput: $paginationInput
    barCategoriesInput: $barCategoriesInput
  ) {
    ...BarCategoriesPaginationInfo
    docs {
      id
      name
      description
      image
      icon
      createdAt
      updatedAt
    }
  }
}

mutation CreateBarCategory($input: CreateBarCategoryInput!) {
  createBarCategory(createBarCategoryInput: $input) {
    id
  }
}

mutation UpdateBarCategory($input: UpdateBarCategoryInput!) {
  updateBarCategory(updateBarCategoryInput: $input) {
    id
  }
}
