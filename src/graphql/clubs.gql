# import "./fragments.gql"

query Clubs($clubsInput: ClubsInput) {
  clubs(clubsInput: $clubsInput) {
    ...ClubsPaginationInfo
    docs {
      id
      name
      slug
      coverImage
      images
      city {
        id
        name
      }
      neighborhood {
        id
        name
      }
    }
  }
}

mutation CreateClub($input: CreateClubInput!) {
  createClub(createClubInput: $input) {
    id
  }
}

mutation UpdateClub($input: UpdateClubInput!) {
  updateClub(updateClubInput: $input) {
    id
  }
}

query ClubCategories(
  $paginationInput: PaginationInput
  $clubCategoriesInput: ClubCategoriesInput
) {
  clubCategories(
    paginationInput: $paginationInput
    clubCategoriesInput: $clubCategoriesInput
  ) {
    ...ClubCategoriesPaginationInfo
    docs {
      id
      name
      description
      image
      icon
      createdAt
      updatedAt
    }
  }
}

mutation CreateClubCategory($input: CreateClubCategoryInput!) {
  createClubCategory(createClubCategoryInput: $input) {
    id
  }
}

mutation UpdateClubCategory($input: UpdateClubCategoryInput!) {
  updateClubCategory(updateClubCategoryInput: $input) {
    id
  }
}

mutation RemoveClubCategory($id: ID!) {
  deleteClubCategory(id: $id) {
    id
  }
}
