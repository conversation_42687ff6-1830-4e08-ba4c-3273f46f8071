import { useState, useCallback } from 'react'

/**
 * Configuration options for the pagination hook
 */
export interface PaginationConfig {
  /** Initial page number (default: 1) */
  initialPage?: number
  /** Initial number of items per page (default: 10) */
  initialLimit?: number
  /** Available options for items per page dropdown (default: [10, 20, 50, 100]) */
  limitOptions?: number[]
  /** Whether to show the items per page selector (default: true) */
  showLimitSelector?: boolean
  /** Whether to show pagination info text (default: true) */
  showInfo?: boolean
  /** Whether to show first/last page buttons (default: true) */
  showFirstLast?: boolean
  /** Maximum number of page buttons to show (default: 7) */
  maxVisiblePages?: number
}

/**
 * Current pagination state
 */
export interface PaginationState {
  /** Current page number (1-based) */
  page: number
  /** Current number of items per page */
  limit: number
}

/**
 * Pagination handler functions expected by components
 */
export interface PaginationHandlers {
  /** Callback when user navigates to a different page */
  onPageChange: (page: number) => void
  /** Callback when user changes items per page limit */
  onLimitChange: (limit: number) => void
}

/**
 * Generic interface for data table components that need pagination
 *
 * @example
 * ```tsx
 * type UsersDataTableProps = DataTableWithPaginationProps<
 *   UseSuspenseQueryResult<UsersQuery, Error>
 * >
 * ```
 */
export interface DataTableWithPaginationProps<TQueryResult = any>
  extends PaginationHandlers {
  /** The query result containing data and pagination info */
  queryResult: TQueryResult
}

/**
 * All available pagination actions and navigation methods
 */
export interface PaginationActions {
  /** Handler for page changes (same as onPageChange) */
  onPageChange: (newPage: number) => void
  /** Handler for limit changes (same as onLimitChange) */
  onLimitChange: (newLimit: number) => void
  /** Direct setter for page number */
  setPage: (page: number) => void
  /** Direct setter for items per page limit */
  setLimit: (limit: number) => void
  /** Reset pagination to initial values */
  reset: () => void
  /** Navigate to first page */
  goToFirstPage: () => void
  /** Navigate to last page */
  goToLastPage: (totalPages: number) => void
  /** Navigate to next page if available */
  goToNextPage: (hasNextPage: boolean) => void
  /** Navigate to previous page if available */
  goToPrevPage: (hasPrevPage: boolean) => void
}

/**
 * Complete return type from usePagination hook
 * Includes all state, handlers, and configuration options
 * Can be spread directly into Pagination component
 */
export interface PaginationReturn
  extends PaginationState,
    PaginationActions,
    PaginationHandlers {
  /** Ready-to-use object for GraphQL queries */
  paginationInput: PaginationState
  /** Available options for items per page dropdown */
  limitOptions: number[]
  /** Whether to show the items per page selector */
  showLimitSelector?: boolean
  /** Whether to show pagination info text */
  showInfo?: boolean
  /** Whether to show first/last page buttons */
  showFirstLast?: boolean
  /** Maximum number of page buttons to show */
  maxVisiblePages?: number
}

/**
 * Reusable pagination hook for data tables
 *
 * Provides complete pagination state management with handlers that can be:
 * 1. Spread directly into Pagination component
 * 2. Used individually in custom components
 * 3. Passed to data table components
 *
 * @param config - Configuration object with initial values and display options
 * @returns Complete pagination object with state, handlers, and configuration
 *
 * @example
 * ```tsx
 * // Basic usage
 * const pagination = usePagination({
 *   initialPage: 1,
 *   initialLimit: 10,
 *   limitOptions: [5, 10, 25, 50],
 *   showLimitSelector: true,
 *   showInfo: true,
 *   showFirstLast: true
 * })
 *
 * // Use in GraphQL query
 * const usersQuery = useSuspenseUsersQuery({
 *   paginationInput: pagination.paginationInput
 * })
 *
 * // Method 1: Spread pagination object directly (recommended)
 * <Pagination paginationInfo={usersQuery.data.users} {...pagination} />
 *
 * // Method 2: Use in data table component
 * <DataTable
 *   queryResult={usersQuery}
 *   onPageChange={pagination.handlePageChange}
 *   onLimitChange={pagination.handleLimitChange}
 * />
 *
 * // Method 3: Use individual navigation methods
 * <Button onClick={pagination.goToFirstPage}>First</Button>
 * <Button onClick={() => pagination.goToPrevPage(data.hasPrevPage)}>Previous</Button>
 * <Button onClick={() => pagination.goToNextPage(data.hasNextPage)}>Next</Button>
 * <Button onClick={() => pagination.goToLastPage(data.totalPages)}>Last</Button>
 * ```
 */
export function usePagination(config: PaginationConfig = {}): PaginationReturn {
  // Extract configuration with sensible defaults
  const {
    initialPage = 1,
    initialLimit = 10,
    limitOptions = [10, 20, 50, 100],
    showLimitSelector = true,
    showInfo = true,
    showFirstLast = true,
    maxVisiblePages = 7,
  } = config

  // Core pagination state
  const [page, setPage] = useState(initialPage)
  const [limit, setLimit] = useState(initialLimit)

  // Primary page change handler with validation
  const handlePageChange = useCallback((newPage: number) => {
    if (newPage >= 1) {
      setPage(newPage)
    }
  }, [])

  // Limit change handler that resets to first page
  const handleLimitChange = useCallback((newLimit: number) => {
    if (newLimit > 0) {
      setLimit(newLimit)
      setPage(1) // Reset to first page when changing limit
    }
  }, [])

  // Navigation helper methods
  const goToFirstPage = useCallback(() => {
    setPage(1)
  }, [])

  const goToLastPage = useCallback((totalPages: number) => {
    if (totalPages >= 1) {
      setPage(totalPages)
    }
  }, [])

  const goToNextPage = useCallback((hasNextPage: boolean) => {
    if (hasNextPage) {
      setPage((prev) => prev + 1)
    }
  }, [])

  const goToPrevPage = useCallback((hasPrevPage: boolean) => {
    if (hasPrevPage) {
      setPage((prev) => Math.max(1, prev - 1))
    }
  }, [])

  // Reset pagination to initial configuration
  const reset = useCallback(() => {
    setPage(initialPage)
    setLimit(initialLimit)
  }, [initialPage, initialLimit])

  // Return comprehensive pagination object
  return {
    // Current state
    page,
    limit,

    // Handlers for components (can be spread into Pagination component)
    onPageChange: handlePageChange,
    onLimitChange: handleLimitChange,

    // Direct state setters
    setPage,
    setLimit,

    // Utility methods
    reset,

    // Navigation methods
    goToFirstPage,
    goToLastPage,
    goToNextPage,
    goToPrevPage,

    // Configuration options (can be spread into Pagination component)
    limitOptions,
    showLimitSelector,
    showInfo,
    showFirstLast,
    maxVisiblePages,

    // Ready-to-use object for GraphQL queries
    paginationInput: {
      page,
      limit,
    },
  }
}
