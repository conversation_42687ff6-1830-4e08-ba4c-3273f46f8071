// Pagination hook for data tables
export { usePagination } from './use-pagination'

// Mobile detection hook
export { useIsMobile } from './use-mobile'

// Pagination-related types and interfaces
export type {
  /** Configuration options for pagination hook */
  PaginationConfig,
  /** Current pagination state (page, limit) */
  PaginationState,
  /** <PERSON><PERSON> functions for pagination events */
  PaginationHandlers,
  /** Generic interface for data table components with pagination */
  DataTableWithPaginationProps,
  /** All pagination actions and navigation methods */
  PaginationActions,
  /** Complete return type from usePagination hook */
  PaginationReturn,
} from './use-pagination'
