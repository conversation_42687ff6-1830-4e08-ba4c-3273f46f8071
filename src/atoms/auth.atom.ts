import { atom, useAtom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'
import { jwtDecode } from 'jwt-decode'
import { useEffect } from 'react'

export type Auth = {
  access_token: string
  userId: string
}

// Create the storage atom with undefined as initial value to detect unloaded state
const authStorageAtom = atomWithStorage<Auth | null | undefined>(
  'user_auth',
  undefined,
)

// Create a derived atom that handles the loading state
export const authAtom = atom((get) => {
  const auth = get(authStorageAtom)
  return {
    auth: auth === undefined ? null : auth,
    isLoading: auth === undefined,
    isInitialized: auth !== undefined,
  }
})

export const useAuth = () => {
  const [authState] = useAtom(authAtom)
  const [, setAuthStorage] = useAtom(authStorageAtom)

  // Force initialization on mount
  useEffect(() => {
    // This will trigger the storage to load if it hasn't already
    const timer = setTimeout(() => {
      if (authState.isLoading) {
        // If still loading after a short delay, assume no stored value exists
        setAuthStorage(null)
      }
    }, 50)

    return () => clearTimeout(timer)
  }, [authState.isLoading, setAuthStorage])

  const login = (access_token: string) => {
    const decoded = jwtDecode(access_token) as { userId: string }
    setAuthStorage({ access_token, userId: decoded.userId })
  }

  const logout = () => {
    setAuthStorage(null)
  }

  const setAuth = (auth: Auth | null) => {
    setAuthStorage(auth)
  }

  return {
    auth: authState.auth,
    isLoading: authState.isLoading,
    isInitialized: authState.isInitialized,
    setAuth,
    login,
    logout,
  }
}
