import { twMerge } from 'tailwind-merge'
import { clsx } from 'clsx'
import type { ClassValue } from 'clsx'
import toast from 'react-hot-toast'
import { isAxiosError } from 'axios'

export function cn(...inputs: Array<ClassValue>) {
  return twMerge(clsx(inputs))
}
export function getDayHoursMins() {
  const timeOfDay = ['AM', 'PM']
  const hours = new Array(12).fill(0).map((_, idx) => idx + 1)
  const mins = []

  for (let i = 0; i < 60; i = i + 5) mins.push(i)

  return { timeOfDay, hours, mins }
}
export async function uploadFile(
  url: string,
  file: File,
): Promise<{ filePath: string }> {
  const myHeaders = new Headers()
  myHeaders.append('Connection', 'keep-alive')
  myHeaders.append('Sec-Fetch-Dest', 'empty')
  myHeaders.append('Sec-Fetch-Mode', 'cors')
  myHeaders.append('Sec-Fetch-Site', 'same-site')

  const formdata = new FormData()
  formdata.append('file', file, file.name)

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_ENDPOINT}/communication/upload-file`,
    {
      method: 'POST',
      headers: myHeaders,
      body: formdata,
      redirect: 'follow',
    },
  )

  if (response.status === 500) {
    // @ts-ignore
    return toast.error('Only images are allowed')
  }

  return await response.json()
}

export const toS3ImageUrl = (filePath: string) =>
  `${process.env.NEXT_PUBLIC_S3_BUCKET_BASE_URL}${filePath}`

interface ToastPromiseParams<TResult = unknown, TError = Error> {
  asyncFunc: Promise<TResult>
  success?: string
  error?: string
  loading?: string
  onSuccess?: (data: TResult) => void
  onError?: (err: TError) => void
}
export function toastPromise<TResult = unknown, TError = Error>({
  asyncFunc,
  error = 'Something went wrong...',
  success = 'Success',
  loading = 'Loading...',
  onError,
  onSuccess,
}: ToastPromiseParams<TResult, TError>) {
  return toast.promise(asyncFunc, {
    loading,
    success: (data: TResult) => {
      onSuccess?.(data)
      return success
    },
    error: (err: TError) => {
      onError?.(err)
      if (err instanceof Error) {
        return err.message
      } else return error
    },
  })
}
export function errorHandler(ex: unknown) {
  let errorMessage = 'Something went wrong'
  if (isAxiosError(ex)) {
    errorMessage = ex.response?.data?.message || ex.message || 'Error…'
  } else if (ex instanceof Error) errorMessage = ex.message

  // toast.error(errorMessage)
}

/**
 * Transform image URL for optimization
 * Currently returns the original URL, optimization logic will be added later
 * @param imageUrl - The original image URL
 * @param options - Optional transformation options (for future use)
 * @returns The transformed image URL
 */
export function transformImageUrl(
  imageUrl: string,
  options?: {
    width?: number
    height?: number
    quality?: number
    format?: 'webp' | 'jpeg' | 'png'
  },
): string {
  // TODO: Add image optimization logic here
  // For now, just return the original URL
  return imageUrl
}
