import { AppSidebar } from '@/components/app-sidebar'
import { PageLoadingFallback } from '@/components/loading-fallback'
import { ModeToggle } from '@/components/theme-toggle'
import { DynamicBreadcrumb } from '@/components/dynamic-breadcrumb'
import { Separator } from '@/components/ui/separator'
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar'
import { Outlet, useLocation } from '@tanstack/react-router'
import { Suspense } from 'react'

export default function BaseLayout() {
  const location = useLocation()

  if (location.pathname.startsWith('/auth')) {
    return (
      <Suspense fallback={<PageLoadingFallback />}>
        <Outlet />
      </Suspense>
    )
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <DynamicBreadcrumb />
          </div>
          <div className="ml-auto px-4">
            <ModeToggle />
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <Outlet />
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
