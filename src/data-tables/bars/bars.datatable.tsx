import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { BarsQuery } from '@/generated/graphql'
import { UseSuspenseQueryResult } from '@tanstack/react-query'
import { transformImageUrl } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Link } from '@tanstack/react-router'
import { Edit } from 'lucide-react'

/**
 * Bars data table component with integrated pagination
 *
 * Demonstrates the reusable pattern for data table components
 * using the DataTableWithPaginationProps interface.
 *
 * @example
 * ```tsx
 * <BarsDataTable
 *   queryResult={barsQuery}
 *   onPageChange={pagination.handlePageChange}
 *   onLimitChange={pagination.handleLimitChange}
 * />
 * ```
 */

// Type definition using the reusable generic interface

export default function BarsDataTable(
  queryResult: UseSuspenseQueryResult<BarsQuery, Error>,
) {
  // Extract bars data from the query result
  const bars = queryResult.data?.bars

  // Handle loading/empty state
  if (!bars) {
    return <div>No bars data available</div>
  }

  return (
    <div className="flex-1 space-y-4">
      {/* Bars Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Cover Image</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Slug</TableHead>
            <TableHead>City</TableHead>
            <TableHead>Neighborhood</TableHead>
            <TableHead>Images</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {bars.docs.map((bar) => (
            <TableRow key={bar.id}>
              <TableCell>
                {bar.coverImage ? (
                  <img
                    src={transformImageUrl(bar.coverImage, {
                      width: 64,
                      height: 64,
                    })}
                    alt={bar.name}
                    className="w-16 h-16 object-cover rounded"
                  />
                ) : (
                  '-'
                )}
              </TableCell>
              <TableCell className="font-medium">{bar.name}</TableCell>
              <TableCell>
                <Badge variant="secondary">{bar.slug}</Badge>
              </TableCell>
              <TableCell>{bar.city.name}</TableCell>
              <TableCell>{bar.neighborhood?.name || '-'}</TableCell>
              <TableCell>
                <Badge variant="outline">
                  {bar.images.length} image
                  {bar.images.length !== 1 ? 's' : ''}
                </Badge>
              </TableCell>
              <TableCell>
                <Button variant="outline" size="sm" asChild>
                  <Link to="/locations/bars/edit" search={{ slug: bar.slug }}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Link>
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
