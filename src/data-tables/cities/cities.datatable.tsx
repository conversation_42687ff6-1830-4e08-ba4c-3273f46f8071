import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { CitiesQuery } from '@/generated/graphql'
import { UseSuspenseQueryResult } from '@tanstack/react-query'
import { transformImageUrl } from '@/lib/utils'
import { Link } from '@tanstack/react-router'
import { Edit } from 'lucide-react'

/**
 * Cities data table component with integrated pagination
 *
 * Demonstrates the reusable pattern for data table components
 * using the DataTableWithPaginationProps interface.
 *
 * @example
 * ```tsx
 * <CitiesDataTable
 *   queryResult={citiesQuery}
 *   onPageChange={pagination.handlePageChange}
 *   onLimitChange={pagination.handleLimitChange}
 * />
 * ```
 */

// Type definition using the reusable generic interface

export default function CitiesDataTable(
  queryResult: UseSuspenseQueryResult<CitiesQuery, Error>,
) {
  // Extract cities data from the query result
  const cities = queryResult.data?.cities

  // Handle loading/empty state
  if (!cities) {
    return <div>No cities data available</div>
  }

  return (
    <div className="flex-1 space-y-4">
      {/* Cities Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Cover Image</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Heading</TableHead>
            <TableHead>Sub Heading</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {cities.docs.map((city) => (
            <TableRow key={city.id}>
              <TableCell>
                {city.coverImage ? (
                  <img
                    src={transformImageUrl(city.coverImage, {
                      width: 64,
                      height: 64,
                    })}
                    alt={city.name}
                    className="w-16 h-16 object-cover rounded"
                  />
                ) : (
                  '-'
                )}
              </TableCell>
              <TableCell className="font-medium">{city.name}</TableCell>
              <TableCell>{city.heading}</TableCell>
              <TableCell>{city.subHeading || '-'}</TableCell>
              <TableCell>
                <Button variant="outline" size="sm" asChild>
                  <Link
                    to="/locations/cities/edit"
                    search={{ name: city.name }}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Link>
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
