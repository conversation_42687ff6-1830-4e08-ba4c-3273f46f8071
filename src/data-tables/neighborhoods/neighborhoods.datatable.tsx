import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { NeighborhoodsQuery } from '@/generated/graphql'
import { UseSuspenseQueryResult } from '@tanstack/react-query'
import { transformImageUrl } from '@/lib/utils'
import { Link } from '@tanstack/react-router'
import { Edit } from 'lucide-react'

/**
 * Neighborhoods data table component with integrated pagination
 *
 * Demonstrates the reusable pattern for data table components
 * using the DataTableWithPaginationProps interface.
 *
 * @example
 * ```tsx
 * <NeighborhoodsDataTable
 *   queryResult={neighborhoodsQuery}
 *   onPageChange={pagination.handlePageChange}
 *   onLimitChange={pagination.handleLimitChange}
 * />
 * ```
 */

// Type definition using the reusable generic interface

export default function NeighborhoodsDataTable(
  queryResult: UseSuspenseQueryResult<NeighborhoodsQuery, Error>,
) {
  // Extract neighborhoods data from the query result
  const neighborhoods = queryResult.data?.neighborhoods

  // Handle loading/empty state
  if (!neighborhoods) {
    return <div>No neighborhoods data available</div>
  }

  return (
    <div className="flex-1 space-y-4">
      {/* Neighborhoods Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Cover Image</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Slug</TableHead>
            <TableHead>City</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {neighborhoods.docs.map((neighborhood) => (
            <TableRow key={neighborhood.id}>
              <TableCell>
                {neighborhood.coverImage ? (
                  <img
                    src={transformImageUrl(neighborhood.coverImage, {
                      width: 64,
                      height: 64,
                    })}
                    alt={neighborhood.name}
                    className="w-16 h-16 object-cover rounded"
                  />
                ) : (
                  '-'
                )}
              </TableCell>
              <TableCell className="font-medium">{neighborhood.name}</TableCell>
              <TableCell>{neighborhood.slug}</TableCell>
              <TableCell>{neighborhood.city.name}</TableCell>
              <TableCell>
                <Button variant="outline" size="sm" asChild>
                  <Link
                    to="/locations/neighborhoods/edit"
                    search={{ name: neighborhood.name }}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Link>
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
