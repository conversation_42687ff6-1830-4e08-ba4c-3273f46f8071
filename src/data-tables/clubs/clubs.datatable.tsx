import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { ClubsQuery } from '@/generated/graphql'
import { UseSuspenseQueryResult } from '@tanstack/react-query'
import { transformImageUrl } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Link } from '@tanstack/react-router'
import { Edit } from 'lucide-react'

/**
 * Clubs data table component with integrated pagination
 *
 * Demonstrates the reusable pattern for data table components
 * using the DataTableWithPaginationProps interface.
 *
 * @example
 * ```tsx
 * <ClubsDataTable
 *   queryResult={clubsQuery}
 *   onPageChange={pagination.handlePageChange}
 *   onLimitChange={pagination.handleLimitChange}
 * />
 * ```
 */

// Type definition using the reusable generic interface

export default function ClubsDataTable(
  queryResult: UseSuspenseQueryResult<ClubsQuery, Error>,
) {
  // Extract clubs data from the query result
  const clubs = queryResult.data?.clubs

  // Handle loading/empty state
  if (!clubs) {
    return <div>No clubs data available</div>
  }

  return (
    <div className="flex-1 space-y-4">
      {/* Clubs Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Cover Image</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Slug</TableHead>
            <TableHead>City</TableHead>
            <TableHead>Neighborhood</TableHead>
            <TableHead>Images</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {clubs.docs.map((club) => (
            <TableRow key={club.id}>
              <TableCell>
                {club.coverImage ? (
                  <img
                    src={transformImageUrl(club.coverImage, {
                      width: 64,
                      height: 64,
                    })}
                    alt={club.name}
                    className="w-16 h-16 object-cover rounded"
                  />
                ) : (
                  '-'
                )}
              </TableCell>
              <TableCell className="font-medium">{club.name}</TableCell>
              <TableCell>
                <Badge variant="secondary">{club.slug}</Badge>
              </TableCell>
              <TableCell>{club.city.name}</TableCell>
              <TableCell>{club.neighborhood?.name || '-'}</TableCell>
              <TableCell>
                <Badge variant="outline">
                  {club.images.length} image
                  {club.images.length !== 1 ? 's' : ''}
                </Badge>
              </TableCell>
              <TableCell>
                <Button variant="outline" size="sm" asChild>
                  <Link to="/locations/clubs/edit" search={{ slug: club.slug }}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Link>
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
