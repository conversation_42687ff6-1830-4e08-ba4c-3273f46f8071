import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { ClubCategoriesQuery } from '@/generated/graphql'
import { UseSuspenseQueryResult } from '@tanstack/react-query'
import { transformImageUrl } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { format } from 'date-fns'
import { Link } from '@tanstack/react-router'
import { Edit } from 'lucide-react'
import * as LucideIcons from 'lucide-react'

/**
 * Club Categories data table component with integrated pagination
 *
 * Displays club categories with their details including name, description,
 * image, icon, and timestamps.
 *
 * @example
 * ```tsx
 * <ClubCategoriesDataTable
 *   queryResult={clubCategoriesQuery}
 * />
 * ```
 */

// Helper function to render Lucide icon
const renderIcon = (iconName?: string | null) => {
  if (!iconName) return '-'

  try {
    // Get the icon component from Lucide
    const IconComponent = (LucideIcons as any)[iconName]
    if (IconComponent) {
      return <IconComponent className="w-5 h-5" />
    }
    return <Badge variant="outline">{iconName}</Badge>
  } catch {
    return <Badge variant="outline">{iconName}</Badge>
  }
}

export default function ClubCategoriesDataTable(
  queryResult: UseSuspenseQueryResult<ClubCategoriesQuery, Error>,
) {
  // Extract club categories data from the query result
  const clubCategories = queryResult.data?.clubCategories

  // Handle loading/empty state
  if (!clubCategories) {
    return <div>No club categories data available</div>
  }

  return (
    <div className="flex-1 space-y-4">
      {/* Club Categories Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Image</TableHead>
            <TableHead>Icon</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Created</TableHead>
            <TableHead>Updated</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {clubCategories.docs.map((category) => (
            <TableRow key={category.id}>
              <TableCell>
                {category.image ? (
                  <img
                    src={transformImageUrl(category.image, {
                      width: 48,
                      height: 48,
                    })}
                    alt={category.name}
                    className="w-12 h-12 object-cover rounded"
                  />
                ) : (
                  '-'
                )}
              </TableCell>
              <TableCell>{renderIcon(category.icon)}</TableCell>
              <TableCell className="font-medium">{category.name}</TableCell>
              <TableCell className="max-w-xs">
                {category.description ? (
                  <span className="text-sm text-muted-foreground line-clamp-2">
                    {category.description}
                  </span>
                ) : (
                  '-'
                )}
              </TableCell>
              <TableCell>
                <span className="text-sm text-muted-foreground">
                  {format(new Date(category.createdAt), 'MMM dd, yyyy')}
                </span>
              </TableCell>
              <TableCell>
                <span className="text-sm text-muted-foreground">
                  {format(new Date(category.updatedAt), 'MMM dd, yyyy')}
                </span>
              </TableCell>
              <TableCell>
                <Button variant="outline" size="sm" asChild>
                  <Link
                    to="/locations/clubs/categories/edit"
                    search={{ name: category.name }}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Link>
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
