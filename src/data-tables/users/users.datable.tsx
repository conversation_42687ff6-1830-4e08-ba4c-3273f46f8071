import { UsersQuery } from '@/generated/graphql'
import { UseSuspenseQueryResult } from '@tanstack/react-query'
import { Pagination } from '@/components/ui/pagination'
import { DataTableWithPaginationProps } from '@/hooks'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

/**
 * Users data table component with integrated pagination
 *
 * Uses the reusable DataTableWithPaginationProps interface to ensure
 * consistent pagination handling across all data tables.
 *
 * @example
 * ```tsx
 * <UsersDataTable
 *   queryResult={usersQuery}
 *   onPageChange={pagination.handlePageChange}
 *   onLimitChange={pagination.handleLimitChange}
 * />
 * ```
 */

// Type definition using the reusable generic interface
type UsersDataTableProps = DataTableWithPaginationProps<
  UseSuspenseQueryResult<UsersQuery, Error>
>

export default function UsersDataTable({
  queryResult,
  onPageChange,
  onLimitChange,
}: UsersDataTableProps) {
  // Extract users data from the query result
  const users = queryResult.data?.users

  // Handle loading/empty state
  if (!users) {
    return <div>No users data available</div>
  }

  return (
    <div className="flex-1 space-y-4">
      {/* Users Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Role</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.docs.map((user) => (
            <TableRow key={user.id}>
              <TableCell className="font-medium">{user.fullname}</TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>{user.role}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Integrated Pagination Component */}
      <Pagination
        paginationInfo={users}
        onPageChange={onPageChange}
        onLimitChange={onLimitChange}
        showLimitSelector={true}
        showInfo={true}
        showFirstLast={true}
      />
    </div>
  )
}
