import { Link, useRouterState } from '@tanstack/react-router'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Home } from 'lucide-react'
import type { BreadcrumbItem as BreadcrumbItemType } from '@/routes/__root'

/**
 * Dynamic breadcrumb component that generates breadcrumbs based on router context
 * Uses TanStack Router's router context to accumulate breadcrumb data from matched routes
 */
export function DynamicBreadcrumb() {
  const matches = useRouterState({ select: (s) => s.matches })

  // Generate breadcrumbs from matched routes that have breadcrumb context
  const breadcrumbs = matches
    .filter((match) => (match.context as any)?.breadcrumb)
    .map((match) => {
      const breadcrumb = (match.context as any).breadcrumb as BreadcrumbItemType
      return {
        title: breadcrumb.title,
        href: breadcrumb.href || match.pathname,
        pathname: match.pathname,
      }
    })

  // Always include home as the first breadcrumb if we have any breadcrumbs
  const allBreadcrumbs =
    breadcrumbs.length > 0
      ? [{ title: 'Dashboard', href: '/', pathname: '/' }, ...breadcrumbs]
      : []

  if (allBreadcrumbs.length === 0) {
    return null
  }

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {allBreadcrumbs.map((breadcrumb, index) => {
          const isLast = index === allBreadcrumbs.length - 1
          const isFirst = index === 0

          return (
            <div key={breadcrumb.pathname} className="flex items-center">
              <BreadcrumbItem className={isFirst ? 'hidden md:flex' : ''}>
                {isLast ? (
                  <BreadcrumbPage className="flex items-center gap-1">
                    {isFirst && <Home className="h-4 w-4" />}
                    {breadcrumb.title}
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link
                      to={breadcrumb.href}
                      className="flex items-center gap-1"
                    >
                      {isFirst && <Home className="h-4 w-4" />}
                      {breadcrumb.title}
                    </Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {!isLast && (
                <BreadcrumbSeparator
                  className={isFirst ? 'hidden md:block' : ''}
                />
              )}
            </div>
          )
        })}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
