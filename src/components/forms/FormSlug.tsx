'use client'

import { useFormContext } from 'react-hook-form'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import type { Form } from '@/@types/Form'

type FormSlugProps = Form & {
  placeholder?: string
}

// Export the sanitizeSlug function for use in other components
export const sanitizeSlug = (value: string): string => {
  return value
    .toLowerCase()
    .replace(/\s+/g, '-') // Convert spaces to hyphens first
    .replace(/[^a-z0-9\-]/g, '') // Remove invalid characters but keep hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

export function FormSlug({
  name,
  label,
  placeholder,
  description,
}: FormSlugProps) {
  const methods = useFormContext()

  const handleSpaceKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === ' ') {
      e.preventDefault()
      const input = e.target as HTMLInputElement
      const cursorPos = input.selectionStart || 0
      const currentValue = input.value
      const newValue =
        currentValue.slice(0, cursorPos) + '-' + currentValue.slice(cursorPos)
      methods.setValue(name, newValue)
      // Set cursor position after the inserted hyphen
      setTimeout(() => {
        input.setSelectionRange(cursorPos + 1, cursorPos + 1)
      }, 0)
    }
  }

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <Input
              placeholder={placeholder}
              {...field}
              onKeyDown={handleSpaceKeyPress}
              onChange={(e) => {
                const slug = sanitizeSlug(e.target.value)
                field.onChange(slug)
              }}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
