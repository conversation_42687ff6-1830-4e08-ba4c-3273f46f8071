'use client'

import { useForm, useFormContext } from 'react-hook-form'
import { Button } from '@/components/ui/button'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

import { useEffect, useState } from 'react'
import { Input } from '../ui/input'
import { PlusSquare } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { Form } from '@/@types/Form'

type Props = Form & {
  options: Array<{ value: string | number; label: string }>
  placeholder?: string
  showSearch?: boolean
  searchInputPlaceholder?: string
  allowAddItem?: boolean
  disabled?: boolean
  className?: string
}

export function FormSelect({
  name,
  description,
  label,
  options,
  placeholder,
  showSearch = false,
  allowAddItem = false,
  disabled = false,
  className,
}: Props) {
  const methods = useFormContext()
  const [searchTerm, setSearchTerm] = useState('')
  const [optionsState, setOptionsState] = useState(options)

  useEffect(() => setOptionsState(options), [options])

  const handleAddItem = () => {
    setOptionsState((options) => {
      // check if exists
      const itemExists = options.some(({ label }) => label === searchTerm)

      if (!itemExists) {
        return [...options, { label: searchTerm, value: searchTerm }]
      }
      return options
    })

    setSearchTerm('')
    setTimeout(() => {
      methods.setValue(name, searchTerm)
    }, 100)
  }
  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <Select
            disabled={disabled}
            onValueChange={(v) => {
              setSearchTerm('')
              field.onChange(v)
            }}
            defaultValue={field.value}
            value={field.value}
          >
            <FormControl>
              <SelectTrigger className={cn('bg-white', className)}>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {showSearch && (
                <div className="w-full p-4 mb-3">
                  <Input
                    placeholder="Search"
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value)
                    }}
                  />
                  {searchTerm && allowAddItem && (
                    <Button
                      className="mt-3"
                      type="button"
                      onClick={handleAddItem}
                    >
                      <PlusSquare className="mr-2 h-4 w-4" /> {searchTerm}
                    </Button>
                  )}
                </div>
              )}

              {optionsState
                .filter(({ label }) =>
                  (label ?? '')
                    .toLowerCase()
                    .replace(/\s/g, '')
                    .includes(searchTerm.toLowerCase()),
                )
                .map(({ label, value }, idx) => {
                  return (
                    <div key={idx}>
                      <SelectItem
                        className="text-xs font-medium"
                        value={value.toString()}
                      >
                        {label}
                      </SelectItem>
                    </div>
                  )
                })}
            </SelectContent>
          </Select>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
