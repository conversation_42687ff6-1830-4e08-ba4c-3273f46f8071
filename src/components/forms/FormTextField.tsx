'use client'

import { useFormContext } from 'react-hook-form'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'
import type { Form } from '@/@types/Form'

type Props = Form & { placeholder?: string; className?: string }

export function FormTextField({
  name,
  description,
  label,
  placeholder,
  className,
  disabled,
}: Props) {
  const methods = useFormContext()

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <Textarea
              disabled={disabled}
              placeholder={placeholder}
              className={cn('resize-none', className)}
              {...field}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
