import _ from 'lodash'
import { useFormContext } from 'react-hook-form'

export default function FormError({ name }: { name: string }) {
  const methods = useFormContext()

  const error = _.get(methods.formState.errors, name)

  if (!name) throw new Error('Form<PERSON>ield must have a name')

  return (
    <>
      {error?.message && (
        <p
          className="mt-2 text-xs text-destructive"
          role="alert"
          aria-live="polite"
        >
          {typeof error.message === 'string'
            ? error.message
            : 'something went wrong'}
        </p>
      )}
    </>
  )
}
