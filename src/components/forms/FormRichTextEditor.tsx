import '@blocknote/core/fonts/inter.css'
import { useCreateBlockNote } from '@blocknote/react'
import { BlockNoteView } from '@blocknote/mantine'
import '@blocknote/mantine/style.css'
import { Controller } from 'react-hook-form'
import { toS3ImageUrl, uploadFile } from '@/lib/utils'

export default function FormRichTextEditor({ name }: { name: string }) {
  // Creates a new editor instance.
  const editor = useCreateBlockNote({
    uploadFile: async (file) => {
      const { filePath } = await uploadFile('/communication/upload-file', file)

      if (!filePath) throw new Error('Only images are allowed')
      return toS3ImageUrl(filePath)
    },
  })

  return (
    <Controller
      name={name}
      render={({ field: { value, onChange } }) => {
        return (
          <BlockNoteView
            theme="light"
            editor={editor}
            className="border shadow-sm rounded-lg p-4"
            onChange={async () => {
              const html = await editor.blocksToHTMLLossy(editor.document)
              onChange(html)
            }}
          />
        )
      }}
    />
  )
}
