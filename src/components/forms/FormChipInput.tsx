'use client'
import { useFormContext } from 'react-hook-form'
import {
  FormField as FF,
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { useState } from 'react'
import type { Form } from '@/@types/Form'
import type { InputProps } from '../ui/input'
import type { ChipOption } from '../ui/chip-input'
import ChipInput from '../ui/chip-input'

type Props = Form & InputProps & { options?: Array<ChipOption> }

export default function FormChipInput({
  name,
  label,
  options,
  ...otherProps
}: Props) {
  const methods = useFormContext()

  const [value, setValue] = useState('')

  return (
    <FF
      name={name}
      render={({ field: { value: fieldValue, onChange, ...rest } }) => {
        const onChipAction = (v: string) => {
          const chips: Array<ChipOption> = [...(fieldValue ?? []), v]
          methods.setValue(name, chips)
          setValue('')
        }

        const onChipSelect = (v: string) => {
          methods.setValue(
            name,
            ((fieldValue ?? []) as Array<string>).filter((value) => value !== v),
          )
        }

        const chips = ((fieldValue ?? []) as Array<string>).map((v) => {
          const label = options?.find((o) => o.value === v)?.label ?? v
          return { label, value: v }
        })

        return (
          <FormItem>
            <div className="items-center flex gap-1">
              {label && <FormLabel>{label}</FormLabel>}
            </div>
            <FormControl>
              <ChipInput
                chips={chips}
                onChipAction={onChipAction}
                onChipSelect={onChipSelect}
                value={value}
                onChange={(e) => {
                  setValue(e.target.value)
                }}
                {...rest}
                {...otherProps}
              />
            </FormControl>

            <FormMessage />
          </FormItem>
        )
      }}
    />
  )
}
