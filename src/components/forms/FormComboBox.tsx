'use client'

import { <PERSON>Icon, ChevronsUpDown } from 'lucide-react'
import { useFormContext } from 'react-hook-form'
import { useState, useMemo } from 'react'

import type { Form } from '@/@types/Form'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { cn } from '@/lib/utils'

export function FormComboBox({
  label,
  name,
  options,
  placeholder,
  onChange,
  disabled,
}: {
  options: Array<{ label: string; value: string }>
  onChange?: (value: string) => void
  disabled?: boolean
} & Form) {
  const methods = useFormContext()
  const [searchValue, setSearchValue] = useState('')

  // Filter options based on search value for better performance
  const filteredOptions = useMemo(() => {
    if (!searchValue) return options
    return options.filter((option) =>
      option.label.toLowerCase().includes(searchValue.toLowerCase()),
    )
  }, [options, searchValue])

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel>{label}</FormLabel>
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  role="combobox"
                  disabled={disabled}
                  className={cn(
                    'justify-between',
                    !field.value && 'text-muted-foreground',
                  )}
                >
                  {field.value
                    ? options.find((item) => item.value === field.value)?.label
                    : placeholder || 'Select Item'}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="p-0 left-0">
              <Command shouldFilter={false}>
                <CommandInput
                  placeholder={placeholder || ''}
                  className="h-9"
                  value={searchValue}
                  onValueChange={setSearchValue}
                />
                <CommandEmpty>No items found.</CommandEmpty>
                <CommandGroup>
                  {filteredOptions.map((item) => (
                    <CommandItem
                      value={item.label}
                      key={item.value}
                      onSelect={() => {
                        field.onChange(item.value)
                        // Call the optional onChange callback for additional handling
                        onChange?.(item.value)
                        // Clear search when item is selected
                        setSearchValue('')
                      }}
                    >
                      {item.label}
                      <CheckIcon
                        className={cn(
                          'ml-auto h-4 w-4',
                          item.value === field.value
                            ? 'opacity-100'
                            : 'opacity-0',
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
