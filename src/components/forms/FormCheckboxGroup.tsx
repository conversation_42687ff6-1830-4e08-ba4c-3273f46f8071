'use client'

import { useForm, useFormContext } from 'react-hook-form'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { cn } from '@/lib/utils'
import type { Form } from '@/@types/Form'

type Props = Form & {
  options: Array<{
    label: string
    value: string
  }>
}
export function FormCheckBoxGroup({
  name,
  label,
  options,
  placeholder,
  disabled,
  orientation = 'vertical',
}: Props & { orientation?: 'vertical' | 'horizontal' }) {
  const methods = useFormContext()

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <div className="mb-4">
            <FormLabel className="text-base">{label}</FormLabel>
            <FormDescription>{placeholder}</FormDescription>
          </div>
          <div
            className={cn(
              orientation === 'vertical'
                ? ' space-y-3'
                : 'flex flex-row items-center  flex-wrap gap-4',
            )}
          >
            {options.map(({ label, value }) => (
              <FormField
                key={value}
                name={name}
                render={({ field }) => {
                  return (
                    <FormItem
                      key={value}
                      className="flex flex-row items-start space-x-3 space-y-0.5"
                    >
                      <FormControl>
                        <Checkbox
                          checked={field.value?.includes(value)}
                          onCheckedChange={(checked) => {
                            return checked
                              ? field.onChange([...(field.value || []), value])
                              : field.onChange(
                                  field.value?.filter(
                                    (v: string) => v !== value,
                                  ),
                                )
                          }}
                          disabled={disabled}
                        />
                      </FormControl>
                      <FormLabel className="font-normal">{label}</FormLabel>
                    </FormItem>
                  )
                }}
              />
            ))}
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
