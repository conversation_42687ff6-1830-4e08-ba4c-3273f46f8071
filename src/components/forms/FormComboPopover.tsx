'use client'

import { useFormContext } from 'react-hook-form'
import { Checkbox } from '@/components/ui/checkbox'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  
  
  ComboboxPopover
} from '../ui/form-combo-popover'
import type {ComboBoxOption, ComboPopoverProps} from '../ui/form-combo-popover';
import type { Form } from '@/@types/Form'

type Props = Form & { options: Array<ComboBoxOption> }

export function FormComboBoxPopover({
  name,
  placeholder,
  label,
  options,
  optionsClassName,
  ...other
}: Props & ComboPopoverProps) {
  const methods = useFormContext()

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <div className="w-full">
          <ComboboxPopover
            label={label || ''}
            options={options}
            value={field.value}
            onChange={field.onChange}
            placeholder={placeholder}
            allowCustomValues={false}
            optionsClassName={optionsClassName}
            {...other}
          />
          <FormMessage />
        </div>
      )}
    />
  )
}
