'use client'

import type { Form } from '@/@types/Form'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { TimePicker } from '@/components/ui/time-picker/time-picker'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { DayOfWeek } from '@/generated/graphql'
import { formatTimings } from '@/lib/time.utils'
import { Clock, Copy, Plus, Trash2 } from 'lucide-react'
import { useState } from 'react'
import { useFieldArray, useFormContext } from 'react-hook-form'

// Type for a single day timing
export type DayTiming = {
  day: DayOfWeek
  timings: [number, number] // Always exactly 2 numbers: [openTime, closeTime]
}

// Type for the business hours schedule
export type BusinessHoursSchedule = {
  schedule: DayTiming[]
}

type FormBusinessHoursProps = Form & {
  name: string
  label?: string
  description?: string
}

export function FormBusinessHours({
  name,
  label = 'Business Hours',
  description = 'Set opening and closing times for each day',
}: FormBusinessHoursProps) {
  const methods = useFormContext()
  const [expandedDays, setExpandedDays] = useState<Set<string>>(new Set())

  const {
    fields: scheduleFields,
    append: appendSchedule,
    remove: removeSchedule,
  } = useFieldArray({
    control: methods.control,
    name: `${name}.schedule`,
  })

  // Helper function to toggle day expansion
  const toggleDay = (day: string) => {
    const newExpanded = new Set(expandedDays)
    if (newExpanded.has(day)) {
      newExpanded.delete(day)
    } else {
      newExpanded.add(day)
    }
    setExpandedDays(newExpanded)
  }

  // Helper function to add a new day schedule
  const addDaySchedule = () => {
    const usedDays = scheduleFields.map((field: any) => field.day)
    const availableDays = Object.values(DayOfWeek).filter(
      (day) => !usedDays.includes(day),
    )

    if (availableDays.length > 0) {
      appendSchedule({
        day: availableDays[0],
        timings: [900, 1700], // Default: 9:00 AM to 5:00 PM
      })
    }
  }

  // Helper function to convert Date to HHMM number
  const dateToTimeNumber = (date: Date | undefined): number => {
    if (!date) return 900 // Default to 9:00 AM
    return date.getHours() * 100 + date.getMinutes()
  }

  // Helper function to convert HHMM number to Date
  const timeNumberToDate = (timeNumber: number): Date => {
    const hours = Math.floor(timeNumber / 100)
    const minutes = timeNumber % 100
    const date = new Date()
    date.setHours(hours, minutes, 0, 0)
    return date
  }

  // Helper function to copy timings from one day to all other days
  const copyTimingsToAllDays = (sourceIndex: number) => {
    // Get current form values for the schedule array
    const scheduleValues = methods.getValues(`${name}.schedule`) as any[]
    if (!scheduleValues || !Array.isArray(scheduleValues)) return

    const sourceDay = scheduleValues[sourceIndex]
    if (!sourceDay || !sourceDay.timings || sourceDay.timings.length !== 2)
      return

    const sourceTimings = sourceDay.timings

    // Update all other days with the same timings
    scheduleValues.forEach((_, index: number) => {
      if (index !== sourceIndex) {
        methods.setValue(
          `${name}.schedule.${index}.timings.0`,
          sourceTimings[0],
        )
        methods.setValue(
          `${name}.schedule.${index}.timings.1`,
          sourceTimings[1],
        )
      }
    })
  }

  return (
    <FormField
      control={methods.control}
      name={name}
      render={() => (
        <FormItem>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <FormLabel className="text-lg font-semibold">{label}</FormLabel>
                {description && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {description}
                  </p>
                )}
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addDaySchedule}
                disabled={scheduleFields.length >= 7}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Day
              </Button>
            </div>

            <div className="space-y-3">
              {scheduleFields.map((scheduleField: any, index: number) => {
                const dayValue = methods.watch(`${name}.schedule.${index}.day`)
                const timingsValue = methods.watch(
                  `${name}.schedule.${index}.timings`,
                )
                const isExpanded = expandedDays.has(`${index}-${dayValue}`)

                return (
                  <Card key={scheduleField.id} className="relative">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <CardTitle className="text-base">
                            {dayValue || 'Select Day'}
                          </CardTitle>
                          {timingsValue && timingsValue.length === 2 && (
                            <Badge variant="secondary" className="text-xs">
                              {formatTimings(timingsValue)}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => copyTimingsToAllDays(index)}
                                disabled={
                                  !timingsValue ||
                                  timingsValue.length !== 2 ||
                                  scheduleFields.length <= 1
                                }
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Copy all values to other days</p>
                            </TooltipContent>
                          </Tooltip>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleDay(`${index}-${dayValue}`)}
                          >
                            <Clock className="h-4 w-4" />
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeSchedule(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>

                    {isExpanded && (
                      <CardContent className="pt-0">
                        <div className="space-y-4">
                          {/* Day Selection */}
                          <FormField
                            control={methods.control}
                            name={`${name}.schedule.${index}.day`}
                            render={({ field: dayField }) => (
                              <FormItem>
                                <FormLabel>Day of Week</FormLabel>
                                <Select
                                  value={dayField.value}
                                  onValueChange={dayField.onChange}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select Day" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {Object.values(DayOfWeek).map((day) => (
                                      <SelectItem key={day} value={day}>
                                        {day}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Opening Time */}
                          <FormField
                            control={methods.control}
                            name={`${name}.schedule.${index}.timings.0`}
                            render={({ field: openField }) => (
                              <FormItem>
                                <FormLabel>Opening Time</FormLabel>
                                <FormControl>
                                  <div className="border rounded-md p-3">
                                    <TimePicker
                                      date={timeNumberToDate(
                                        openField.value || 900,
                                      )}
                                      setDate={(date) => {
                                        if (date) {
                                          const timeNumber =
                                            dateToTimeNumber(date)
                                          openField.onChange(timeNumber)
                                        }
                                      }}
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Closing Time */}
                          <FormField
                            control={methods.control}
                            name={`${name}.schedule.${index}.timings.1`}
                            render={({ field: closeField }) => (
                              <FormItem>
                                <FormLabel>Closing Time</FormLabel>
                                <FormControl>
                                  <div className="border rounded-md p-3">
                                    <TimePicker
                                      date={timeNumberToDate(
                                        closeField.value || 1700,
                                      )}
                                      setDate={(date) => {
                                        if (date) {
                                          const timeNumber =
                                            dateToTimeNumber(date)
                                          closeField.onChange(timeNumber)
                                        }
                                      }}
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    )}
                  </Card>
                )
              })}
            </div>

            {scheduleFields.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Clock className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p>No business hours set</p>
                <p className="text-sm">Click "Add Day" to set opening hours</p>
              </div>
            )}

            <FormMessage />
          </div>
        </FormItem>
      )}
    />
  )
}
