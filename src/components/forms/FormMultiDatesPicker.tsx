'use client'

import {
  addYears,
  format,
  getHours,
  getMinutes,
  isValid,
  setHours,
  setMinutes,
  subYears,
} from 'date-fns'
import { CalendarIcon } from 'lucide-react'
import { useFormContext } from 'react-hook-form'

import { cn, getDayHoursMins } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar  } from '@/components/ui/calendar'
import type {CalendarProps} from '@/components/ui/calendar';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { ScrollArea } from '../ui/scroll-area'
import { isArray } from 'lodash'
import { Badge } from '../ui/badge'
import type { Form } from '@/@types/Form'

type Props = Form &
  CalendarProps & {
    disabled?: (date: Date) => boolean
    showTimePicker?: boolean
  }

export function FormMultiDatesPicker({
  name,
  description,
  label,
  disabled,
  showTimePicker,
  placeholder,
  dateDisabled,
  ...props
}: Props & { dateDisabled?: boolean }) {
  const methods = useFormContext()

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-col">
          {label && <FormLabel>{label}</FormLabel>}
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant={'outline'}
                  className={cn(
                    ' pl-3 text-left font-normal flex border   items-center justify-between rounded-[8px] h-full',
                    !field.value && 'text-muted-foreground ',
                  )}
                >
                  <div className="flex-1">
                    {field.value ? (
                      Array.isArray(field.value) ? (
                        field.value.length ? (
                          <div>
                            <span className="block text-muted-foreground mb-1">
                              Selected Dates:
                            </span>
                            <div className="grid grid-cols-3 gap-2  ">
                              {field.value.map((date, idx) => (
                                <Badge key={idx} className="w-fit">
                                  {format(date, 'dd/MM/yyyy')}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">
                            Select dates
                          </span>
                        )
                      ) : (
                        <span>
                          {format(
                            field.value,
                            `PPP${showTimePicker ? ' h:mm a' : ''}`,
                          )}
                        </span>
                      )
                    ) : (
                      <span className="text-muted-foreground">
                        Select a date
                      </span>
                    )}
                  </div>

                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 flex flex-row" align="start">
              <Calendar
                className=""
                mode="multiple"
                selected={field.value}
                onSelect={field.onChange}
                disabled={dateDisabled}
                initialFocus
                captionLayout="dropdown"
                fromDate={subYears(new Date(), 100)}
                toDate={addYears(new Date(), 100)}
                {...props}
              />
            </PopoverContent>
          </Popover>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
