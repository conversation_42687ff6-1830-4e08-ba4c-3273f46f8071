import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { useCreateSignedUploadUrlMutation } from '@/generated/graphql'
import { useId, useState, useEffect } from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import Dropzone from 'shadcn-dropzone'
import { X } from 'lucide-react'
import { GenericForm } from '@/@types/Form'

export type FileState = {
  file: File
  progress: number
  uploadedUrl?: string
}

interface FormPresignedUploadProps {
  name: string
  label?: string
  accept?: string
  multiple?: boolean
}

// Add a helper function to extract filename from URL
const getFileNameFromUrl = (url: string) => {
  try {
    const urlPath = new URL(url).pathname
    return decodeURIComponent(urlPath.split('/').pop() || '')
  } catch {
    return 'Unknown file'
  }
}

export function FormPresignedUpload({
  name,
  label,
  accept,
  multiple = false,
}: GenericForm<FormPresignedUploadProps>) {
  const id = useId()
  const [files, setFiles] = useState<FileState[]>([])
  const [error, setError] = useState<string | null>(null)
  const { mutateAsync: getSignedUrl } = useCreateSignedUploadUrlMutation()

  // Auto-clear error after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError(null)
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [error])

  // Add form context
  const methods = useFormContext()
  const formValue = methods.watch(name)

  // Effect to handle existing file URL(s)
  useEffect(() => {
    if (formValue && files.length === 0) {
      if (multiple && Array.isArray(formValue)) {
        // Handle multiple existing URLs
        const existingFiles = formValue.map((url) => ({
          file: new File([], getFileNameFromUrl(url)), // Empty file with name from URL
          progress: 100,
          uploadedUrl: url,
        }))
        setFiles(existingFiles)
      } else if (!multiple && typeof formValue === 'string') {
        // Handle single existing URL
        setFiles([
          {
            file: new File([], getFileNameFromUrl(formValue)), // Empty file with name from URL
            progress: 100,
            uploadedUrl: formValue,
          },
        ])
      }
    } else if (
      !formValue ||
      (Array.isArray(formValue) && formValue.length === 0)
    ) {
      setFiles([])
    }
  }, [formValue, multiple])

  const uploadFile = async (file: File) => {
    try {
      // Get signed URL with fields
      const data = await getSignedUrl({
        input: {
          key: `uploads/${Date.now()}-${file.name}`,
          contentType: file.type, // This should match exactly what S3 expects
          expiresIn: 300,
        },
      })

      if (
        !data?.createSignedUploadUrl?.url ||
        !data?.createSignedUploadUrl?.fields
      ) {
        throw new Error('Failed to get upload URL')
      }

      // Create FormData with fields in specific order
      const formData = new FormData()
      const fields = data.createSignedUploadUrl.fields

      // Add fields in correct order for S3
      formData.append('key', fields.key)
      formData.append('bucket', fields.bucket)
      formData.append('acl', fields.acl)
      formData.append('X-Amz-Algorithm', fields.algorithm)
      formData.append('X-Amz-Credential', fields.credential)
      formData.append('X-Amz-Date', fields.date)
      formData.append('Policy', fields.Policy)
      formData.append('X-Amz-Signature', fields.signature)

      // Important: Use the exact Content-Type from the policy
      formData.append('Content-Type', file.type) // Use file's content type

      // Add the file last
      formData.append('file', file)

      // Upload using XMLHttpRequest
      const xhr = new XMLHttpRequest()
      xhr.open('POST', data.createSignedUploadUrl.url)

      // Don't set any content type header, let the browser set it with the form data
      return new Promise((resolve, reject) => {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100)
            setFiles((current) =>
              current.map((f) => (f.file === file ? { ...f, progress } : f)),
            )
          }
        })

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            // Construct the final S3 URL
            const fileUrl = `${data.createSignedUploadUrl.url}/${fields.key}`
            resolve(fileUrl)
          } else {
            reject(new Error('Upload failed'))
          }
        }

        xhr.onerror = () => reject(new Error('Upload failed'))
        xhr.send(formData)
      })
    } catch (error) {
      console.error('Upload failed:', error)
      throw error
    }
  }

  // Modify the render part to show existing files
  return (
    <Controller
      name={name}
      render={({ field: { onChange }, fieldState: { error: fieldError } }) => {
        // Helper function to remove a file
        const removeFile = (indexToRemove: number) => {
          const updatedFiles = files.filter(
            (_, index) => index !== indexToRemove,
          )
          setFiles(updatedFiles)
          setError(null)

          if (multiple) {
            const updatedUrls = updatedFiles
              .filter((f) => f.uploadedUrl)
              .map((f) => f.uploadedUrl!)
            const newValue = updatedUrls.length > 0 ? updatedUrls : null
            onChange(newValue)
            methods.setValue(name, newValue)
          } else {
            onChange(null)
            methods.setValue(name, null)
          }
        }

        return (
          <div className="space-y-2">
            {label && <Label htmlFor={id}>{label}</Label>}
            {files.length > 0 ? (
              <div className="grid gap-2">
                {files.map((fileState, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-2 p-2 border rounded"
                  >
                    <div className="flex-1">
                      <div className="text-sm">
                        {fileState.uploadedUrl ? (
                          <a
                            href={fileState.uploadedUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            {fileState.file.name}
                          </a>
                        ) : (
                          fileState.file.name
                        )}
                      </div>
                      {fileState.progress < 100 && !fileState.uploadedUrl && (
                        <Progress
                          value={fileState.progress}
                          className="h-1 mt-1"
                        />
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => removeFile(index)}
                      className="text-muted-foreground hover:text-destructive transition-colors"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            ) : null}

            {(!multiple && files.length === 0) || multiple ? (
              <Dropzone
                onDrop={async (droppedFiles) => {
                  // Check if multiple files are being dropped in single file mode
                  if (!multiple && droppedFiles.length > 1) {
                    setError(
                      `You can only upload one file at a time. You selected ${droppedFiles.length} files.`,
                    )
                    return
                  }

                  // Check if trying to add files when one already exists in single file mode
                  if (!multiple && files.length > 0) {
                    setError(
                      'You can only upload one file at a time. Please remove the existing file first.',
                    )
                    return
                  }

                  setError(null)

                  // For single file mode, clear existing files
                  // For multiple file mode, keep existing files and add new ones
                  const existingFiles = multiple ? files : []

                  const newFiles = multiple ? droppedFiles : [droppedFiles[0]]

                  const newFileStates = newFiles.map((file) => ({
                    file,
                    progress: 0,
                  }))

                  // Combine existing files with new files for multiple mode
                  const allFileStates = [...existingFiles, ...newFileStates]
                  setFiles(allFileStates)

                  try {
                    const uploadResults = await Promise.all(
                      newFileStates.map(async ({ file }) => {
                        const uploadedUrl = await uploadFile(file)
                        return { file, uploadedUrl }
                      }),
                    )

                    // Update the files with upload results
                    setFiles((current) =>
                      current.map((fileState) => {
                        const result = uploadResults.find(
                          (r) => r.file === fileState.file,
                        )
                        return result
                          ? {
                              ...fileState,
                              uploadedUrl: result.uploadedUrl as string,
                            }
                          : fileState
                      }),
                    )

                    if (multiple) {
                      // For multiple files, combine existing URLs with new URLs
                      const existingUrls = existingFiles
                        .filter((f) => f.uploadedUrl)
                        .map((f) => f.uploadedUrl!)
                      const newUrls = uploadResults.map(
                        (r) => r.uploadedUrl as string,
                      )
                      const allUrls = [...existingUrls, ...newUrls]
                      onChange(allUrls)
                      methods.setValue(name, allUrls)
                    } else {
                      // For single file, just use the uploaded URL
                      const newValue = uploadResults[0].uploadedUrl
                      onChange(newValue)
                      methods.setValue(name, newValue)
                    }
                  } catch (error) {
                    console.error('Upload failed:', error)
                    setError('Upload failed. Please try again.')
                    // Restore previous state on error
                    setFiles(existingFiles)
                    if (!multiple) {
                      methods.setValue(name, null)
                    }
                  }
                }}
                maxFiles={multiple ? undefined : 1}
                multiple={multiple}
                accept={
                  accept
                    ? accept
                        .split(',')
                        .reduce<Record<string, string[]>>((acc, type) => {
                          const trimmed = type.trim()
                          if (trimmed) acc[trimmed] = []
                          return acc
                        }, {})
                    : undefined
                }
                containerClassName="border rounded-2xl border-dashed"
                onDropRejected={(rejectedFiles) => {
                  if (!multiple && rejectedFiles.length > 0) {
                    const totalFiles = rejectedFiles.length
                    if (totalFiles > 1) {
                      setError(
                        `You can only upload one file at a time. You selected ${totalFiles} files.`,
                      )
                    } else {
                      // Handle other rejection reasons (file type, size, etc.)
                      const rejection = rejectedFiles[0]
                      if (
                        rejection.errors.some(
                          (e) => e.code === 'too-many-files',
                        )
                      ) {
                        setError('You can only upload one file at a time.')
                      } else if (
                        rejection.errors.some(
                          (e) => e.code === 'file-invalid-type',
                        )
                      ) {
                        setError(
                          'Invalid file type. Please check the accepted file formats.',
                        )
                      } else {
                        setError(
                          'File rejected. Please check the file and try again.',
                        )
                      }
                    }
                  }
                }}
              />
            ) : null}

            {(error || fieldError) && (
              <p className="text-sm text-destructive">
                {error || fieldError?.message}
              </p>
            )}
          </div>
        )
      }}
    />
  )
}
