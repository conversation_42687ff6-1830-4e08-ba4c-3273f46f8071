'use client'

import { useFormContext } from 'react-hook-form'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Slider } from '@/components/ui/slider'
import type { Form } from '@/@types/Form'

interface FormSliderProps extends Form {
  min?: number
  max?: number
  step?: number
  showValue?: boolean
}

/**
 * FormSlider component for handling slider inputs in forms
 * Supports single value sliders with customizable min, max, and step values
 */
export function FormSlider({
  name,
  label,
  description,
  min = 0,
  max = 5,
  step = 0.1,
  showValue = true,
  disabled,
}: FormSliderProps) {
  const methods = useFormContext()

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem className="space-y-3">
          <div className="flex items-center justify-between">
            {label && <FormLabel>{label}</FormLabel>}
            {showValue && (
              <span className="text-sm text-muted-foreground">
                {field.value?.toFixed(1) || min}
              </span>
            )}
          </div>
          <FormControl>
            <Slider
              min={min}
              max={max}
              step={step}
              value={[field.value || min]}
              onValueChange={(values) => field.onChange(values[0])}
              disabled={disabled}
              className="w-full"
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}

export default FormSlider
