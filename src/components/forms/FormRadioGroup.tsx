'use client'

import { useFormContext } from 'react-hook-form'

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import type { Form } from '@/@types/Form'

type Props = Form & {
  options: Array<{
    label: string
    value: string
  }>
}
export function FormRadioGroup({ name, label, options, disabled }: Props) {
  const methods = useFormContext()

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => (
        <FormItem className="space-y-3">
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <RadioGroup
              disabled={disabled}
              onValueChange={field.onChange}
              defaultValue={field.value}
              value={field.value}
              className="flex items-end space-y-1 h-5 gap-5"
            >
              {options.map(({ label, value }, idx) => {
                return (
                  <FormItem
                    key={idx}
                    className="flex items-center space-x-2 space-y-0"
                  >
                    <FormControl>
                      <RadioGroupItem
                        disabled={disabled}
                        value={value}
                        className="flex items-center"
                      />
                    </FormControl>
                    <FormLabel className="font-normal">{label}</FormLabel>
                  </FormItem>
                )
              })}
            </RadioGroup>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
