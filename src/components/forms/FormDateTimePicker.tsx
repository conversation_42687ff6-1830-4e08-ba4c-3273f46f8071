'use client'

import { format } from 'date-fns'
import { Calendar as CalendarIcon } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar  } from '@/components/ui/calendar'
import type {CalendarProps} from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form'

import { TimePicker } from '../ui/time-picker/time-picker'
import type { Form } from '@/@types/Form'

type Props = Form & CalendarProps

export function FormDateTimePicker({
  name,
  description,
  label,
  placeholder,
  horizontal,
  disableDate,
  removeDate,
  disableTime,
  ...props
}: Props & { horizontal?: boolean } & { disableDate?: boolean } & {
  removeDate?: boolean
} & { disableTime?: boolean }) {
  return (
    <FormField
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel className="text-left">{label}</FormLabel>
          <Popover>
            <FormControl>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !field.value && 'text-muted-foreground',
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {field.value ? (
                    removeDate ? (
                      format(field.value, ' HH:mm:ss')
                    ) : (
                      format(field.value, 'PPP HH:mm:ss')
                    )
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
            </FormControl>
            <PopoverContent
              className={cn(
                'w-auto p-0',
                horizontal ? 'flex items-center' : '',
              )}
            >
              {disableDate ? null : (
                <Calendar
                  mode="single"
                  selected={field.value}
                  onSelect={field.onChange}
                  initialFocus
                  {...props}
                />
              )}
              <div className="p-3 border-t border-border">
                <TimePicker
                  setDate={field.onChange}
                  date={field.value}
                  disabled={disableTime}
                />
              </div>
            </PopoverContent>
          </Popover>
        </FormItem>
      )}
    />
  )
}
