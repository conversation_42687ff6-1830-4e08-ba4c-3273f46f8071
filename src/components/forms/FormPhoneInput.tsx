'use client'

import { useFormContext } from 'react-hook-form'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { PhoneInput } from '@/components/ui/phone-input'
import type { Form } from '@/@types/Form'
import * as RPNInput from 'react-phone-number-input'
import { parsePhoneNumber } from 'react-phone-number-input'
import * as z from 'zod'

/**
 * Zod schema for phone number validation
 * Validates the structure: { countryCode: string, phone: string }
 */
export const phoneSchema = z.object({
  /** Country code (e.g., +1, +91) */
  countryCode: z.string().min(1, 'Country code is required'),
  /** Phone number */
  phone: z.string().min(1, 'Phone number is required'),
})

type FormPhoneInputProps = Form & {
  placeholder?: string
  disabled?: boolean
  className?: string
}

/**
 * FormPhoneInput component that integrates PhoneInput with react-hook-form
 *
 * This component handles phone numbers in the format:
 * {
 *   countryCode: string (e.g., "+1", "+91")
 *   phone: string (the phone number without country code)
 * }
 *
 * @example
 * ```tsx
 * <FormPhoneInput
 *   name="contact"
 *   label="Phone Number"
 *   placeholder="Enter phone number"
 * />
 * ```
 */
export function FormPhoneInput({
  name,
  label,
  description,
  placeholder = 'Enter phone number',
  disabled = false,
  className,
}: FormPhoneInputProps) {
  const methods = useFormContext()

  return (
    <FormField
      control={methods.control}
      name={name}
      render={({ field }) => {
        // Get current values from the form
        const currentValue = field.value || {}
        const countryCode = currentValue.countryCode || '+1'
        const phone = currentValue.phone || ''

        // Combine countryCode and phone to create full phone number for PhoneInput
        const fullPhoneNumber = phone ? `${countryCode}${phone}` : ''

        const handlePhoneChange = (value: RPNInput.Value) => {
          if (!value) {
            // If no value, clear both fields
            field.onChange({
              countryCode: '+1',
              phone: '',
            })
            return
          }

          try {
            // Parse the phone number to extract country code and national number
            const phoneNumber = parsePhoneNumber(value)

            if (phoneNumber) {
              const newCountryCode = `+${phoneNumber.countryCallingCode}`
              const newPhone = phoneNumber.nationalNumber

              field.onChange({
                countryCode: newCountryCode,
                phone: newPhone,
              })
            } else {
              // If parsing fails, try to extract country code manually
              const match = value.match(/^(\+\d{1,4})(.*)$/)
              if (match) {
                const [, extractedCountryCode, extractedPhone] = match
                field.onChange({
                  countryCode: extractedCountryCode,
                  phone: extractedPhone.replace(/\D/g, ''), // Remove non-digits
                })
              } else {
                // Fallback: assume it's just a phone number with default country code
                field.onChange({
                  countryCode: '+1',
                  phone: value.replace(/\D/g, ''),
                })
              }
            }
          } catch (error) {
            console.warn('Error parsing phone number:', error)
            // Fallback handling
            field.onChange({
              countryCode: '+1',
              phone: value.replace(/\D/g, ''),
            })
          }
        }

        return (
          <FormItem>
            {label && <FormLabel>{label}</FormLabel>}
            <FormControl>
              <PhoneInput
                value={fullPhoneNumber as RPNInput.Value}
                onChange={handlePhoneChange}
                placeholder={placeholder}
                disabled={disabled}
                className={className}
                defaultCountry="US"
                international={false}
              />
            </FormControl>
            {description && <FormDescription>{description}</FormDescription>}
            <FormMessage />
          </FormItem>
        )
      }}
    />
  )
}
