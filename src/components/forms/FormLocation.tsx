import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Libraries,
  GoogleMap,
  Marker,
  Autocomplete,
} from '@react-google-maps/api'
import { useJsApiLoader } from '@react-google-maps/api'
import { useState, useCallback, useEffect } from 'react'
import { useFormContext } from 'react-hook-form'
import FormError from './FormError.tsx'
import { VITE_GOOGLE_MAPS_API_KEY } from '@/env'
import { Search } from 'lucide-react'
import { GenericForm } from '@/@types/Form'
import * as z from 'zod'

const libraries: Libraries = ['places']

// Updated schema to only include center coordinates
export const locationSchema = z.object({
  center: z.array(z.number()).length(2),
})

export default function FormLocation({
  name,
  label,
  placeholder,
}: GenericForm<{
  name: string
  label: string
  placeholder?: string
}>) {
  const { setValue, watch } = useFormContext()
  const value = watch(name)
  const [searchBox, setSearchBox] =
    useState<google.maps.places.Autocomplete | null>(null)
  const [markerPosition, setMarkerPosition] =
    useState<google.maps.LatLngLiteral | null>(null)

  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: VITE_GOOGLE_MAPS_API_KEY,
    libraries,
  })

  // Default center (Kuala Lumpur)
  const [center, setCenter] = useState<google.maps.LatLngLiteral>({
    lat: 3.140853,
    lng: 101.693207,
  })

  const onMapLoad = useCallback((_map: google.maps.Map) => {
    // Map loaded callback
  }, [])

  // Handle place selection from autocomplete
  const onPlaceChanged = () => {
    if (searchBox) {
      const place = searchBox.getPlace()
      const location = place.geometry?.location
      if (location) {
        const newPosition = { lat: location.lat(), lng: location.lng() }
        setCenter(newPosition)
        setMarkerPosition(newPosition)
        // Update form value with selected location
        setValue(name, {
          center: [newPosition.lat, newPosition.lng],
        })
      }
    }
  }

  // Handle map click to place marker
  const onMapClick = (e: google.maps.MapMouseEvent) => {
    if (e.latLng) {
      const newPosition = { lat: e.latLng.lat(), lng: e.latLng.lng() }
      setMarkerPosition(newPosition)
      setCenter(newPosition)
      // Update form value with clicked location
      setValue(name, {
        center: [newPosition.lat, newPosition.lng],
      })
    }
  }

  // Initialize marker position from existing form value on component mount
  useEffect(() => {
    if (value?.center) {
      const position = {
        lat: value.center[0],
        lng: value.center[1],
      }
      setCenter(position)
      setMarkerPosition(position)
    }
  }, [value])

  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      {isLoaded && (
        <div className="relative">
          <Autocomplete onLoad={setSearchBox} onPlaceChanged={onPlaceChanged}>
            <div className="relative mb-2">
              <Search className="absolute left-3 top-1/2 -translate-y-1/3 text-gray-500 h-4 w-4" />
              <Input
                placeholder={placeholder || 'Search location'}
                className="pl-10"
              />
            </div>
          </Autocomplete>
        </div>
      )}
      <div className="h-[70vh] relative">
        {isLoaded ? (
          <GoogleMap
            onLoad={onMapLoad}
            mapContainerClassName="h-[70vh] w-full flex flex-col"
            center={center}
            zoom={12}
            onClick={onMapClick}
          >
            {/* Show marker only when a location is selected */}
            {markerPosition && (
              <Marker
                position={markerPosition}
                draggable
                onDragEnd={(e) => {
                  if (e.latLng) {
                    const newPosition = {
                      lat: e.latLng.lat(),
                      lng: e.latLng.lng(),
                    }
                    setMarkerPosition(newPosition)
                    setCenter(newPosition)
                    setValue(name, {
                      center: [newPosition.lat, newPosition.lng],
                    })
                  }
                }}
              />
            )}
          </GoogleMap>
        ) : (
          <div className="h-full flex items-center justify-center">
            Loading...
          </div>
        )}
      </div>
      <FormError name={name} />
    </div>
  )
}
