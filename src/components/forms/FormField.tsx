'use client'
import type { Form } from '@/@types/Form'
import {
  FormField as FF,
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useFormContext } from 'react-hook-form'

type Props = Form & React.InputHTMLAttributes<HTMLInputElement>

export default function FormField({
  name,
  label,
  description,
  className,
  ...rest
}: Props) {
  const methods = useFormContext()

  return (
    <FF
      control={methods.control}
      name={name}
      render={({ field }) => {
        return (
          <FormItem>
            <div className="items-center flex">
              {label && <FormLabel>{label}</FormLabel>}
            </div>
            <FormControl>
              <Input className={className} {...field} {...rest} />
            </FormControl>
            {description && <FormDescription>{description}</FormDescription>}
            <FormMessage />
          </FormItem>
        )
      }}
    />
  )
}
