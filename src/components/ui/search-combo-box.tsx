'use client'

import * as React from 'react'
import { Check, ChevronsUpDown } from 'lucide-react'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

type Option = { value: string; label: string }
type Props = {
  placeholder?: string
  label?: string
  onChange?: (value: string) => void
  options?: Array<Option>
  displayValue?: string
  onValueSelect?: (value: string) => void
  className?: string
  searchClassName?: string
}

export function SearchComboBox({
  label,
  placeholder,
  onChange,
  displayValue,
  options = [],
  onValueSelect,
  className,
  searchClassName,
}: Props) {
  const [open, setOpen] = React.useState(false)
  const [value, setValue] = React.useState('')
  const [searchTerm, setSearchTerm] = React.useState('')

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn('w-[200px] justify-between', className)}
        >
          {displayValue
            ? displayValue
            : value
              ? options.find((option) => option.value === value)?.label
              : placeholder || 'Select'}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className={cn('w-[300px] p-0', searchClassName)}>
        {label}
        <Command shouldFilter={false}>
          <CommandInput
            placeholder={placeholder}
            value={searchTerm}
            onValueChange={(v) => {
              setSearchTerm(v)
              onChange?.(v)
            }}
          />
          <CommandEmpty>No item found.</CommandEmpty>
          <CommandGroup>
            {options.map((item) => (
              <CommandItem
                key={item.value}
                value={item.value}
                onSelect={(currentValue) => {
                  setValue(currentValue === value ? '' : currentValue)
                  onValueSelect?.(currentValue)
                  setOpen(false)
                }}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    value === item.value ? 'opacity-100' : 'opacity-0',
                  )}
                />
                {item.label}
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
