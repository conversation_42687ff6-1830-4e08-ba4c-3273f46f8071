'use client'

import * as React from 'react'

import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '../ui/badge'
import { Label } from '../ui/label'
import { Check, Plus, X } from 'lucide-react'
import { cn } from '@/lib/utils'

export type ComboBoxOption = {
  value: string
  label: string
  icon?: React.JSX.Element // TODO: work on icon
}

export type ComboPopoverProps = {
  label: string
  placeholder?: string
  options: Array<ComboBoxOption>
  onValueSelect?: (data?: string) => void
  onChipSelect?: (data?: string) => void
  allowCustomValues?: boolean
  optionsClassName?: string
  disabled?: boolean
  yes?: boolean
  reverse?: boolean
} & (
  | {
      multiple?: true
      value?: Array<string>
      onChange?: (data: Array<string>) => void
    }
  | {
      multiple?: boolean
      value?: string
      onChange?: (data: string) => void
    }
)

export function ComboboxPopover({
  label,
  placeholder,
  options,
  value,
  onChange,
  onValueSelect,
  onChipSelect,
  multiple = true,
  allowCustomValues,
  optionsClassName,
  disabled = false,
  reverse,
  yes,
}: ComboPopoverProps) {
  const [open, setOpen] = React.useState(false)
  const [optionsState, setOptionsState] = React.useState(options)
  const [searchTerm, setSearchTerm] = React.useState('')

  React.useEffect(() => {
    setOptionsState(options)
  }, [options])

  const _value = React.useMemo(
    () => (typeof value === 'string' ? [value] : value || []),
    [value],
  )

  const handleValue = React.useCallback(
    (v?: string) => {
      onChipSelect?.(v)
      setSearchTerm('')
      onValueSelect?.(v)

      const vExists = _value.some((_) => _ === v)

      if (allowCustomValues && !vExists) {
        // @ts-ignore
        setOptionsState((_) => {
          if (!_.some((_) => _.value === v)) {
            return [..._, { label: v, value: v }]
          }
          return [..._]
        })
      }

      if (multiple) {
        // @ts-ignore
        if (vExists) return onChange?.((_value || []).filter((_) => _ !== v))

        // @ts-ignore
        onChange?.([..._value, v])
        // @ts-ignore
      } else {
        // @ts-ignore
        if (v === value) return onChange?.('')
        // @ts-ignore
        onChange?.(v)
      }
    },
    [
      _value,
      allowCustomValues,
      multiple,
      onChange,
      onChipSelect,
      onValueSelect,
      value,
    ],
  )

  return (
    <div className="w-full ">
      <Label>{label}</Label>

      <div
        className={cn(
          'border rounded-lg p-4 flex flex-col',
          reverse && 'flex-col  -reverse',
        )}
      >
        {!!value?.length && (
          <div className="flex flex-wrap gap-1">
            {(typeof value === 'string' ? [value] : value).map((v) => {
              return (
                <Badge
                  className="cursor-pointer"
                  key={v}
                  onClick={() => {
                    disabled ? null : handleValue(v)
                  }}
                >
                  {
                    optionsState.find((_) => {
                      return _.value === v
                    })?.label
                  }
                  <X className="w-4 h-4 ml-1" />
                </Badge>
              )
            })}
          </div>
        )}
        <div className="mb-6">
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="w-full justify-start mt-4"
                disabled={disabled}
              >
                + Select
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className={`p-0 ${optionsClassName}`}
              side="bottom"
              align="start"
            >
              <Command>
                <CommandInput
                  placeholder={placeholder}
                  onValueChange={setSearchTerm}
                  value={searchTerm}
                />
                <CommandList>
                  <CommandEmpty className="p-4 space-y-2">
                    {allowCustomValues && (
                      <Button
                        type="button"
                        onClick={() => handleValue(searchTerm)}
                      >
                        {searchTerm}
                        <Plus className="w-5 h-5 ml-2" />
                      </Button>
                    )}
                    <p>No results found.</p>
                  </CommandEmpty>
                  <CommandGroup>
                    {allowCustomValues &&
                      optionsState?.every((_) => {
                        return _.label !== searchTerm
                      }) &&
                      searchTerm && (
                        <div className="pl-1 mb-2">
                          <Button variant={'outline'} type="button">
                            {searchTerm}
                            <Plus className="w-5 h-5 ml-2" />
                          </Button>
                        </div>
                      )}
                    {optionsState.map((option) => {
                      // if (option?.value === undefined) return null;
                      const isSelectedItem = (
                        typeof value === 'string' ? [value] : value
                      )?.some((_) => _ === option.value)
                      return (
                        <CommandItem
                          key={option.value}
                          value={option.label}
                          onSelect={() => handleValue(option.value)}
                          className={cn(isSelectedItem && 'bg-accent mt-1')}
                        >
                          {option.icon}
                          <span>{option.label}</span>
                        </CommandItem>
                      )
                    })}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  )
}
