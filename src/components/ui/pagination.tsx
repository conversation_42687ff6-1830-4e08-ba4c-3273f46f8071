import {
  ChevronLeftIcon,
  ChevronRightIcon,
  MoreHorizontalIcon,
  ChevronsLeftIcon,
  ChevronsRightIcon,
} from 'lucide-react'

import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'

// Base pagination info interface that matches GraphQL fragments
interface PaginationInfo {
  /** Total number of documents/items in the entire dataset */
  totalDocs: number

  /** Number of items per page (page size) */
  limit: number

  /** Current page number (1-based) */
  page: number

  /** Total number of pages available */
  totalPages: number

  /** Previous page number, null if on first page */
  prevPage?: number | null

  /** Next page number, null if on last page */
  nextPage?: number | null

  /** Whether there is a previous page available */
  hasPrevPage: boolean

  /** Whether there is a next page available */
  hasNextPage: boolean

  /** Starting number for items on current page (e.g., 11 for page 2 with limit 10) */
  pagingCounter?: number | null
}

// Props for the main Pagination component
interface PaginationProps {
  /** GraphQL pagination data containing page info, total counts, and navigation flags */
  paginationInfo: PaginationInfo

  /** Callback function triggered when user navigates to a different page */
  onPageChange: (page: number) => void

  /** Optional callback for when user changes items per page limit. If not provided, limit selector is hidden */
  onLimitChange?: (limit: number) => void

  /** Whether to show the items per page selector dropdown. Requires onLimitChange to be provided */
  showLimitSelector?: boolean

  /** Available options for items per page dropdown. Default: [10, 20, 50, 100] */
  limitOptions?: number[]

  /** Additional CSS classes to apply to the pagination container */
  className?: string

  /** Whether to show pagination info text (e.g., "Showing 1 to 10 of 100 results"). Default: true */
  showInfo?: boolean

  /** Whether to show first/last page navigation buttons. Default: true */
  showFirstLast?: boolean

  /** Maximum number of page buttons to show before using ellipsis. Default: 7 */
  maxVisiblePages?: number
}

// Generate page numbers to display
function generatePageNumbers(
  currentPage: number,
  totalPages: number,
  maxVisible: number = 7,
): (number | 'ellipsis')[] {
  if (totalPages <= maxVisible) {
    return Array.from({ length: totalPages }, (_, i) => i + 1)
  }

  const pages: (number | 'ellipsis')[] = []
  const halfVisible = Math.floor(maxVisible / 2)

  // Always show first page
  pages.push(1)

  if (currentPage <= halfVisible + 2) {
    // Show pages from start
    for (let i = 2; i <= Math.min(maxVisible - 1, totalPages - 1); i++) {
      pages.push(i)
    }
    if (totalPages > maxVisible - 1) {
      pages.push('ellipsis')
    }
  } else if (currentPage >= totalPages - halfVisible - 1) {
    // Show pages from end
    if (totalPages > maxVisible - 1) {
      pages.push('ellipsis')
    }
    for (
      let i = Math.max(2, totalPages - maxVisible + 2);
      i <= totalPages - 1;
      i++
    ) {
      pages.push(i)
    }
  } else {
    // Show pages around current
    pages.push('ellipsis')
    for (
      let i = currentPage - halfVisible + 1;
      i <= currentPage + halfVisible - 1;
      i++
    ) {
      pages.push(i)
    }
    pages.push('ellipsis')
  }

  // Always show last page if more than 1 page
  if (totalPages > 1) {
    pages.push(totalPages)
  }

  return pages
}

function Pagination({
  paginationInfo,
  onPageChange,
  onLimitChange,
  showLimitSelector = true,
  limitOptions = [10, 20, 50, 100],
  className,
  showInfo = true,
  showFirstLast = true,
  maxVisiblePages = 7,
}: PaginationProps) {
  const {
    totalDocs,
    limit,
    page: currentPage,
    totalPages,
    hasPrevPage,
    hasNextPage,
    pagingCounter,
  } = paginationInfo

  const pageNumbers = generatePageNumbers(
    currentPage,
    totalPages,
    maxVisiblePages,
  )

  // Calculate display info
  const startItem = pagingCounter || (currentPage - 1) * limit + 1
  const endItem = Math.min(startItem + limit - 1, totalDocs)

  return (
    <div className={cn('flex flex-col gap-4', className)}>
      {/* Pagination Info */}
      {showInfo && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-sm text-muted-foreground">
          <div>
            Showing {startItem.toLocaleString()} to {endItem.toLocaleString()}{' '}
            of {totalDocs.toLocaleString()} results
          </div>
          {showLimitSelector && onLimitChange && (
            <div className="flex items-center gap-2">
              <span>Show:</span>
              <select
                value={limit}
                onChange={(e) => onLimitChange(Number(e.target.value))}
                className="border border-input bg-background px-2 py-1 rounded-md text-sm"
              >
                {limitOptions.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
              <span>per page</span>
            </div>
          )}
        </div>
      )}

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <nav
          role="navigation"
          aria-label="pagination"
          className="flex items-center justify-center"
        >
          <div className="flex items-center gap-1">
            {/* First Page */}
            {showFirstLast && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(1)}
                disabled={!hasPrevPage}
                className="h-8 w-8 p-0"
                aria-label="Go to first page"
              >
                <ChevronsLeftIcon className="h-4 w-4" />
              </Button>
            )}

            {/* Previous Page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage - 1)}
              disabled={!hasPrevPage}
              className="h-8 px-3"
              aria-label="Go to previous page"
            >
              <ChevronLeftIcon className="h-4 w-4 mr-1" />
              <span className="hidden sm:inline">Previous</span>
            </Button>

            {/* Page Numbers */}
            <div className="flex items-center gap-1">
              {pageNumbers.map((pageNum, index) => {
                if (pageNum === 'ellipsis') {
                  return (
                    <span
                      key={`ellipsis-${index}`}
                      className="flex h-8 w-8 items-center justify-center text-muted-foreground"
                      aria-hidden="true"
                    >
                      <MoreHorizontalIcon className="h-4 w-4" />
                    </span>
                  )
                }

                const isActive = pageNum === currentPage
                return (
                  <Button
                    key={pageNum}
                    variant={isActive ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => onPageChange(pageNum)}
                    className="h-8 w-8 p-0"
                    aria-label={`Go to page ${pageNum}`}
                    aria-current={isActive ? 'page' : undefined}
                  >
                    {pageNum}
                  </Button>
                )
              })}
            </div>

            {/* Next Page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage + 1)}
              disabled={!hasNextPage}
              className="h-8 px-3"
              aria-label="Go to next page"
            >
              <span className="hidden sm:inline">Next</span>
              <ChevronRightIcon className="h-4 w-4 ml-1" />
            </Button>

            {/* Last Page */}
            {showFirstLast && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(totalPages)}
                disabled={!hasNextPage}
                className="h-8 w-8 p-0"
                aria-label="Go to last page"
              >
                <ChevronsRightIcon className="h-4 w-4" />
              </Button>
            )}
          </div>
        </nav>
      )}
    </div>
  )
}

// Export the main component and types
export { Pagination, type PaginationInfo, type PaginationProps }
