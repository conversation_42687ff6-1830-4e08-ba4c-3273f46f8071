import { Skeleton } from '@/components/ui/skeleton'
import { Loader2 } from 'lucide-react'

interface LoadingFallbackProps {
  variant?: 'spinner' | 'skeleton' | 'minimal'
  message?: string
}

export function LoadingFallback({ 
  variant = 'skeleton', 
  message = 'Loading...' 
}: LoadingFallbackProps) {
  if (variant === 'spinner') {
    return (
      <div className="flex min-h-[400px] flex-col items-center justify-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    )
  }

  if (variant === 'minimal') {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
      </div>
    )
  }

  // Default skeleton variant
  return (
    <div className="space-y-4 p-6">
      <div className="space-y-2">
        <Skeleton className="h-8 w-[250px]" />
        <Skeleton className="h-4 w-[200px]" />
      </div>
      
      <div className="space-y-3">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
      
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        ))}
      </div>
    </div>
  )
}

// Specific loading components for different sections
export function PageLoadingFallback() {
  return <LoadingFallback variant="skeleton" message="Loading page..." />
}

export function ComponentLoadingFallback() {
  return <LoadingFallback variant="minimal" />
}

export function DataLoadingFallback() {
  return <LoadingFallback variant="spinner" message="Fetching data..." />
}
