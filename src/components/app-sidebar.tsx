import {
  BarChart3,
  CreditCard,
  FileText,
  GalleryVerticalEnd,
  Home,
  MapPin,
  PieChart,
  Plus,
  Settings2,
  Ticket,
  Users,
  Zap,
} from 'lucide-react'
import * as React from 'react'

import { NavMain } from '@/components/nav-main'
import { NavProjects } from '@/components/nav-projects'
import { NavUser } from '@/components/nav-user'
import { TeamSwitcher } from '@/components/team-switcher'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/sidebar'

// This is sample data.
const data = {
  user: {
    name: 'Seeker',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
  teams: [
    {
      name: 'Seeker',
      logo: GalleryVerticalEnd,
      plan: 'Admin',
    },
  ],
  navMain: [
    {
      title: 'Dashboard',
      url: '/dashboard',
      icon: Home,
      items: [
        {
          title: 'Overview',
          url: '/dashboard',
        },
        {
          title: 'Analytics',
          url: '/dashboard/analytics',
        },
      ],
    },
    {
      title: 'User',
      url: '#',
      icon: Users,
      items: [
        {
          title: 'All Users',
          url: '/users',
        },
        {
          title: 'Admins',
          url: '/users/admins',
        },
        {
          title: 'Customers',
          url: '/users/customers',
        },
      ],
    },
    {
      title: 'Location',
      url: '#',
      icon: MapPin,
      items: [
        {
          title: 'Cities',
          url: '/locations/cities',
        },
        {
          title: 'Neighborhoods',
          url: '/locations/neighborhoods',
        },
        {
          title: 'Bars',
          url: '/locations/bars',
        },
        {
          title: 'Clubs',
          url: '/locations/clubs',
        },
      ],
    },
    {
      title: 'Events',
      url: '#',
      icon: Ticket,
      items: [
        {
          title: 'Bars',
          url: '/events/bars-clubs',
        },
        {
          title: 'Clubs',
          url: '/events/restaurants',
        },
      ],
    },
    {
      title: 'Content',
      url: '#',
      icon: FileText,
      items: [
        {
          title: 'Blogs',
          url: '/content/blogs',
        },
        {
          title: 'CMS Pages',
          url: '/content/pages',
        },
      ],
    },
    {
      title: 'Subscription',
      url: '#',
      icon: CreditCard,
      items: [
        {
          title: 'Bar Subscriptions',
          url: '/subscriptions/bars',
        },
        {
          title: 'Club Subscriptions',
          url: '/subscriptions/clubs',
        },
        {
          title: 'Restaurant Subscriptions',
          url: '/subscriptions/restaurants',
        },
      ],
    },

    {
      title: 'Analytics & Reports',
      url: '#',
      icon: BarChart3,
      items: [
        {
          title: 'Business Insights',
          url: '/analytics/insights',
        },
        {
          title: 'Revenue Reports',
          url: '/analytics/revenue',
        },
        {
          title: 'User Activity',
          url: '/analytics/user-activity',
        },
        {
          title: 'Venue Performance',
          url: '/analytics/venue-performance',
        },
      ],
    },
    {
      title: 'Settings',
      url: '#',
      icon: Settings2,
      items: [
        {
          title: 'App Layout',
          url: '/settings/app-layout',
        },
        {
          title: 'General',
          url: '/settings/general',
        },
        {
          title: 'Platform Configuration',
          url: '/settings/platform',
        },
        {
          title: 'Payment Settings',
          url: '/settings/payments',
        },
        {
          title: 'Email Templates',
          url: '/settings/email-templates',
        },
      ],
    },
  ],
  projects: [
    {
      name: 'Add New Venue',
      url: '/venues/new',
      icon: Plus,
    },
    {
      name: 'Create Event',
      url: '/events/new',
      icon: Ticket,
    },
    {
      name: 'Manage Users',
      url: '/users/manage',
      icon: Users,
    },
    {
      name: 'View Reports',
      url: '/reports',
      icon: PieChart,
    },
    {
      name: 'Quick Setup',
      url: '/setup',
      icon: Zap,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
