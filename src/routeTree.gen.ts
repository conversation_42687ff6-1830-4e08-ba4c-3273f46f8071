/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LocationsRouteImport } from './routes/locations'
import { Route as IndexRouteImport } from './routes/index'
import { Route as UsersIndexRouteImport } from './routes/users/index'
import { Route as AuthLoginRouteImport } from './routes/auth/login'
import { Route as SettingsAppLayoutIndexRouteImport } from './routes/settings/app-layout/index'
import { Route as LocationsNeighborhoodsIndexRouteImport } from './routes/locations/neighborhoods/index'
import { Route as LocationsClubsIndexRouteImport } from './routes/locations/clubs/index'
import { Route as LocationsCitiesIndexRouteImport } from './routes/locations/cities/index'
import { Route as LocationsBarsIndexRouteImport } from './routes/locations/bars/index'
import { Route as SettingsAppLayoutCreateRouteImport } from './routes/settings/app-layout/create'
import { Route as LocationsNeighborhoodsEditRouteImport } from './routes/locations/neighborhoods/edit'
import { Route as LocationsNeighborhoodsCreateRouteImport } from './routes/locations/neighborhoods/create'
import { Route as LocationsClubsEditRouteImport } from './routes/locations/clubs/edit'
import { Route as LocationsClubsCreateRouteImport } from './routes/locations/clubs/create'
import { Route as LocationsCitiesEditRouteImport } from './routes/locations/cities/edit'
import { Route as LocationsCitiesCreateRouteImport } from './routes/locations/cities/create'
import { Route as LocationsBarsEditRouteImport } from './routes/locations/bars/edit'
import { Route as LocationsBarsCreateRouteImport } from './routes/locations/bars/create'
import { Route as LocationsClubsCategoriesIndexRouteImport } from './routes/locations/clubs/categories/index'
import { Route as LocationsBarsCategoriesIndexRouteImport } from './routes/locations/bars/categories/index'
import { Route as LocationsClubsCategoriesEditRouteImport } from './routes/locations/clubs/categories/edit'
import { Route as LocationsClubsCategoriesCreateRouteImport } from './routes/locations/clubs/categories/create'
import { Route as LocationsBarsCategoriesEditRouteImport } from './routes/locations/bars/categories/edit'
import { Route as LocationsBarsCategoriesCreateRouteImport } from './routes/locations/bars/categories/create'

const LocationsRoute = LocationsRouteImport.update({
  id: '/locations',
  path: '/locations',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const UsersIndexRoute = UsersIndexRouteImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthLoginRoute = AuthLoginRouteImport.update({
  id: '/auth/login',
  path: '/auth/login',
  getParentRoute: () => rootRouteImport,
} as any)
const SettingsAppLayoutIndexRoute = SettingsAppLayoutIndexRouteImport.update({
  id: '/settings/app-layout/',
  path: '/settings/app-layout/',
  getParentRoute: () => rootRouteImport,
} as any)
const LocationsNeighborhoodsIndexRoute =
  LocationsNeighborhoodsIndexRouteImport.update({
    id: '/neighborhoods/',
    path: '/neighborhoods/',
    getParentRoute: () => LocationsRoute,
  } as any)
const LocationsClubsIndexRoute = LocationsClubsIndexRouteImport.update({
  id: '/clubs/',
  path: '/clubs/',
  getParentRoute: () => LocationsRoute,
} as any)
const LocationsCitiesIndexRoute = LocationsCitiesIndexRouteImport.update({
  id: '/cities/',
  path: '/cities/',
  getParentRoute: () => LocationsRoute,
} as any)
const LocationsBarsIndexRoute = LocationsBarsIndexRouteImport.update({
  id: '/bars/',
  path: '/bars/',
  getParentRoute: () => LocationsRoute,
} as any)
const SettingsAppLayoutCreateRoute = SettingsAppLayoutCreateRouteImport.update({
  id: '/settings/app-layout/create',
  path: '/settings/app-layout/create',
  getParentRoute: () => rootRouteImport,
} as any)
const LocationsNeighborhoodsEditRoute =
  LocationsNeighborhoodsEditRouteImport.update({
    id: '/neighborhoods/edit',
    path: '/neighborhoods/edit',
    getParentRoute: () => LocationsRoute,
  } as any)
const LocationsNeighborhoodsCreateRoute =
  LocationsNeighborhoodsCreateRouteImport.update({
    id: '/neighborhoods/create',
    path: '/neighborhoods/create',
    getParentRoute: () => LocationsRoute,
  } as any)
const LocationsClubsEditRoute = LocationsClubsEditRouteImport.update({
  id: '/clubs/edit',
  path: '/clubs/edit',
  getParentRoute: () => LocationsRoute,
} as any)
const LocationsClubsCreateRoute = LocationsClubsCreateRouteImport.update({
  id: '/clubs/create',
  path: '/clubs/create',
  getParentRoute: () => LocationsRoute,
} as any)
const LocationsCitiesEditRoute = LocationsCitiesEditRouteImport.update({
  id: '/cities/edit',
  path: '/cities/edit',
  getParentRoute: () => LocationsRoute,
} as any)
const LocationsCitiesCreateRoute = LocationsCitiesCreateRouteImport.update({
  id: '/cities/create',
  path: '/cities/create',
  getParentRoute: () => LocationsRoute,
} as any)
const LocationsBarsEditRoute = LocationsBarsEditRouteImport.update({
  id: '/bars/edit',
  path: '/bars/edit',
  getParentRoute: () => LocationsRoute,
} as any)
const LocationsBarsCreateRoute = LocationsBarsCreateRouteImport.update({
  id: '/bars/create',
  path: '/bars/create',
  getParentRoute: () => LocationsRoute,
} as any)
const LocationsClubsCategoriesIndexRoute =
  LocationsClubsCategoriesIndexRouteImport.update({
    id: '/clubs/categories/',
    path: '/clubs/categories/',
    getParentRoute: () => LocationsRoute,
  } as any)
const LocationsBarsCategoriesIndexRoute =
  LocationsBarsCategoriesIndexRouteImport.update({
    id: '/bars/categories/',
    path: '/bars/categories/',
    getParentRoute: () => LocationsRoute,
  } as any)
const LocationsClubsCategoriesEditRoute =
  LocationsClubsCategoriesEditRouteImport.update({
    id: '/clubs/categories/edit',
    path: '/clubs/categories/edit',
    getParentRoute: () => LocationsRoute,
  } as any)
const LocationsClubsCategoriesCreateRoute =
  LocationsClubsCategoriesCreateRouteImport.update({
    id: '/clubs/categories/create',
    path: '/clubs/categories/create',
    getParentRoute: () => LocationsRoute,
  } as any)
const LocationsBarsCategoriesEditRoute =
  LocationsBarsCategoriesEditRouteImport.update({
    id: '/bars/categories/edit',
    path: '/bars/categories/edit',
    getParentRoute: () => LocationsRoute,
  } as any)
const LocationsBarsCategoriesCreateRoute =
  LocationsBarsCategoriesCreateRouteImport.update({
    id: '/bars/categories/create',
    path: '/bars/categories/create',
    getParentRoute: () => LocationsRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/locations': typeof LocationsRouteWithChildren
  '/auth/login': typeof AuthLoginRoute
  '/users': typeof UsersIndexRoute
  '/locations/bars/create': typeof LocationsBarsCreateRoute
  '/locations/bars/edit': typeof LocationsBarsEditRoute
  '/locations/cities/create': typeof LocationsCitiesCreateRoute
  '/locations/cities/edit': typeof LocationsCitiesEditRoute
  '/locations/clubs/create': typeof LocationsClubsCreateRoute
  '/locations/clubs/edit': typeof LocationsClubsEditRoute
  '/locations/neighborhoods/create': typeof LocationsNeighborhoodsCreateRoute
  '/locations/neighborhoods/edit': typeof LocationsNeighborhoodsEditRoute
  '/settings/app-layout/create': typeof SettingsAppLayoutCreateRoute
  '/locations/bars': typeof LocationsBarsIndexRoute
  '/locations/cities': typeof LocationsCitiesIndexRoute
  '/locations/clubs': typeof LocationsClubsIndexRoute
  '/locations/neighborhoods': typeof LocationsNeighborhoodsIndexRoute
  '/settings/app-layout': typeof SettingsAppLayoutIndexRoute
  '/locations/bars/categories/create': typeof LocationsBarsCategoriesCreateRoute
  '/locations/bars/categories/edit': typeof LocationsBarsCategoriesEditRoute
  '/locations/clubs/categories/create': typeof LocationsClubsCategoriesCreateRoute
  '/locations/clubs/categories/edit': typeof LocationsClubsCategoriesEditRoute
  '/locations/bars/categories': typeof LocationsBarsCategoriesIndexRoute
  '/locations/clubs/categories': typeof LocationsClubsCategoriesIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/locations': typeof LocationsRouteWithChildren
  '/auth/login': typeof AuthLoginRoute
  '/users': typeof UsersIndexRoute
  '/locations/bars/create': typeof LocationsBarsCreateRoute
  '/locations/bars/edit': typeof LocationsBarsEditRoute
  '/locations/cities/create': typeof LocationsCitiesCreateRoute
  '/locations/cities/edit': typeof LocationsCitiesEditRoute
  '/locations/clubs/create': typeof LocationsClubsCreateRoute
  '/locations/clubs/edit': typeof LocationsClubsEditRoute
  '/locations/neighborhoods/create': typeof LocationsNeighborhoodsCreateRoute
  '/locations/neighborhoods/edit': typeof LocationsNeighborhoodsEditRoute
  '/settings/app-layout/create': typeof SettingsAppLayoutCreateRoute
  '/locations/bars': typeof LocationsBarsIndexRoute
  '/locations/cities': typeof LocationsCitiesIndexRoute
  '/locations/clubs': typeof LocationsClubsIndexRoute
  '/locations/neighborhoods': typeof LocationsNeighborhoodsIndexRoute
  '/settings/app-layout': typeof SettingsAppLayoutIndexRoute
  '/locations/bars/categories/create': typeof LocationsBarsCategoriesCreateRoute
  '/locations/bars/categories/edit': typeof LocationsBarsCategoriesEditRoute
  '/locations/clubs/categories/create': typeof LocationsClubsCategoriesCreateRoute
  '/locations/clubs/categories/edit': typeof LocationsClubsCategoriesEditRoute
  '/locations/bars/categories': typeof LocationsBarsCategoriesIndexRoute
  '/locations/clubs/categories': typeof LocationsClubsCategoriesIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/locations': typeof LocationsRouteWithChildren
  '/auth/login': typeof AuthLoginRoute
  '/users/': typeof UsersIndexRoute
  '/locations/bars/create': typeof LocationsBarsCreateRoute
  '/locations/bars/edit': typeof LocationsBarsEditRoute
  '/locations/cities/create': typeof LocationsCitiesCreateRoute
  '/locations/cities/edit': typeof LocationsCitiesEditRoute
  '/locations/clubs/create': typeof LocationsClubsCreateRoute
  '/locations/clubs/edit': typeof LocationsClubsEditRoute
  '/locations/neighborhoods/create': typeof LocationsNeighborhoodsCreateRoute
  '/locations/neighborhoods/edit': typeof LocationsNeighborhoodsEditRoute
  '/settings/app-layout/create': typeof SettingsAppLayoutCreateRoute
  '/locations/bars/': typeof LocationsBarsIndexRoute
  '/locations/cities/': typeof LocationsCitiesIndexRoute
  '/locations/clubs/': typeof LocationsClubsIndexRoute
  '/locations/neighborhoods/': typeof LocationsNeighborhoodsIndexRoute
  '/settings/app-layout/': typeof SettingsAppLayoutIndexRoute
  '/locations/bars/categories/create': typeof LocationsBarsCategoriesCreateRoute
  '/locations/bars/categories/edit': typeof LocationsBarsCategoriesEditRoute
  '/locations/clubs/categories/create': typeof LocationsClubsCategoriesCreateRoute
  '/locations/clubs/categories/edit': typeof LocationsClubsCategoriesEditRoute
  '/locations/bars/categories/': typeof LocationsBarsCategoriesIndexRoute
  '/locations/clubs/categories/': typeof LocationsClubsCategoriesIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/locations'
    | '/auth/login'
    | '/users'
    | '/locations/bars/create'
    | '/locations/bars/edit'
    | '/locations/cities/create'
    | '/locations/cities/edit'
    | '/locations/clubs/create'
    | '/locations/clubs/edit'
    | '/locations/neighborhoods/create'
    | '/locations/neighborhoods/edit'
    | '/settings/app-layout/create'
    | '/locations/bars'
    | '/locations/cities'
    | '/locations/clubs'
    | '/locations/neighborhoods'
    | '/settings/app-layout'
    | '/locations/bars/categories/create'
    | '/locations/bars/categories/edit'
    | '/locations/clubs/categories/create'
    | '/locations/clubs/categories/edit'
    | '/locations/bars/categories'
    | '/locations/clubs/categories'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/locations'
    | '/auth/login'
    | '/users'
    | '/locations/bars/create'
    | '/locations/bars/edit'
    | '/locations/cities/create'
    | '/locations/cities/edit'
    | '/locations/clubs/create'
    | '/locations/clubs/edit'
    | '/locations/neighborhoods/create'
    | '/locations/neighborhoods/edit'
    | '/settings/app-layout/create'
    | '/locations/bars'
    | '/locations/cities'
    | '/locations/clubs'
    | '/locations/neighborhoods'
    | '/settings/app-layout'
    | '/locations/bars/categories/create'
    | '/locations/bars/categories/edit'
    | '/locations/clubs/categories/create'
    | '/locations/clubs/categories/edit'
    | '/locations/bars/categories'
    | '/locations/clubs/categories'
  id:
    | '__root__'
    | '/'
    | '/locations'
    | '/auth/login'
    | '/users/'
    | '/locations/bars/create'
    | '/locations/bars/edit'
    | '/locations/cities/create'
    | '/locations/cities/edit'
    | '/locations/clubs/create'
    | '/locations/clubs/edit'
    | '/locations/neighborhoods/create'
    | '/locations/neighborhoods/edit'
    | '/settings/app-layout/create'
    | '/locations/bars/'
    | '/locations/cities/'
    | '/locations/clubs/'
    | '/locations/neighborhoods/'
    | '/settings/app-layout/'
    | '/locations/bars/categories/create'
    | '/locations/bars/categories/edit'
    | '/locations/clubs/categories/create'
    | '/locations/clubs/categories/edit'
    | '/locations/bars/categories/'
    | '/locations/clubs/categories/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  LocationsRoute: typeof LocationsRouteWithChildren
  AuthLoginRoute: typeof AuthLoginRoute
  UsersIndexRoute: typeof UsersIndexRoute
  SettingsAppLayoutCreateRoute: typeof SettingsAppLayoutCreateRoute
  SettingsAppLayoutIndexRoute: typeof SettingsAppLayoutIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/locations': {
      id: '/locations'
      path: '/locations'
      fullPath: '/locations'
      preLoaderRoute: typeof LocationsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/users/': {
      id: '/users/'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof UsersIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth/login': {
      id: '/auth/login'
      path: '/auth/login'
      fullPath: '/auth/login'
      preLoaderRoute: typeof AuthLoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/settings/app-layout/': {
      id: '/settings/app-layout/'
      path: '/settings/app-layout'
      fullPath: '/settings/app-layout'
      preLoaderRoute: typeof SettingsAppLayoutIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/locations/neighborhoods/': {
      id: '/locations/neighborhoods/'
      path: '/neighborhoods'
      fullPath: '/locations/neighborhoods'
      preLoaderRoute: typeof LocationsNeighborhoodsIndexRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/clubs/': {
      id: '/locations/clubs/'
      path: '/clubs'
      fullPath: '/locations/clubs'
      preLoaderRoute: typeof LocationsClubsIndexRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/cities/': {
      id: '/locations/cities/'
      path: '/cities'
      fullPath: '/locations/cities'
      preLoaderRoute: typeof LocationsCitiesIndexRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/bars/': {
      id: '/locations/bars/'
      path: '/bars'
      fullPath: '/locations/bars'
      preLoaderRoute: typeof LocationsBarsIndexRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/settings/app-layout/create': {
      id: '/settings/app-layout/create'
      path: '/settings/app-layout/create'
      fullPath: '/settings/app-layout/create'
      preLoaderRoute: typeof SettingsAppLayoutCreateRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/locations/neighborhoods/edit': {
      id: '/locations/neighborhoods/edit'
      path: '/neighborhoods/edit'
      fullPath: '/locations/neighborhoods/edit'
      preLoaderRoute: typeof LocationsNeighborhoodsEditRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/neighborhoods/create': {
      id: '/locations/neighborhoods/create'
      path: '/neighborhoods/create'
      fullPath: '/locations/neighborhoods/create'
      preLoaderRoute: typeof LocationsNeighborhoodsCreateRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/clubs/edit': {
      id: '/locations/clubs/edit'
      path: '/clubs/edit'
      fullPath: '/locations/clubs/edit'
      preLoaderRoute: typeof LocationsClubsEditRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/clubs/create': {
      id: '/locations/clubs/create'
      path: '/clubs/create'
      fullPath: '/locations/clubs/create'
      preLoaderRoute: typeof LocationsClubsCreateRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/cities/edit': {
      id: '/locations/cities/edit'
      path: '/cities/edit'
      fullPath: '/locations/cities/edit'
      preLoaderRoute: typeof LocationsCitiesEditRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/cities/create': {
      id: '/locations/cities/create'
      path: '/cities/create'
      fullPath: '/locations/cities/create'
      preLoaderRoute: typeof LocationsCitiesCreateRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/bars/edit': {
      id: '/locations/bars/edit'
      path: '/bars/edit'
      fullPath: '/locations/bars/edit'
      preLoaderRoute: typeof LocationsBarsEditRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/bars/create': {
      id: '/locations/bars/create'
      path: '/bars/create'
      fullPath: '/locations/bars/create'
      preLoaderRoute: typeof LocationsBarsCreateRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/clubs/categories/': {
      id: '/locations/clubs/categories/'
      path: '/clubs/categories'
      fullPath: '/locations/clubs/categories'
      preLoaderRoute: typeof LocationsClubsCategoriesIndexRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/bars/categories/': {
      id: '/locations/bars/categories/'
      path: '/bars/categories'
      fullPath: '/locations/bars/categories'
      preLoaderRoute: typeof LocationsBarsCategoriesIndexRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/clubs/categories/edit': {
      id: '/locations/clubs/categories/edit'
      path: '/clubs/categories/edit'
      fullPath: '/locations/clubs/categories/edit'
      preLoaderRoute: typeof LocationsClubsCategoriesEditRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/clubs/categories/create': {
      id: '/locations/clubs/categories/create'
      path: '/clubs/categories/create'
      fullPath: '/locations/clubs/categories/create'
      preLoaderRoute: typeof LocationsClubsCategoriesCreateRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/bars/categories/edit': {
      id: '/locations/bars/categories/edit'
      path: '/bars/categories/edit'
      fullPath: '/locations/bars/categories/edit'
      preLoaderRoute: typeof LocationsBarsCategoriesEditRouteImport
      parentRoute: typeof LocationsRoute
    }
    '/locations/bars/categories/create': {
      id: '/locations/bars/categories/create'
      path: '/bars/categories/create'
      fullPath: '/locations/bars/categories/create'
      preLoaderRoute: typeof LocationsBarsCategoriesCreateRouteImport
      parentRoute: typeof LocationsRoute
    }
  }
}

interface LocationsRouteChildren {
  LocationsBarsCreateRoute: typeof LocationsBarsCreateRoute
  LocationsBarsEditRoute: typeof LocationsBarsEditRoute
  LocationsCitiesCreateRoute: typeof LocationsCitiesCreateRoute
  LocationsCitiesEditRoute: typeof LocationsCitiesEditRoute
  LocationsClubsCreateRoute: typeof LocationsClubsCreateRoute
  LocationsClubsEditRoute: typeof LocationsClubsEditRoute
  LocationsNeighborhoodsCreateRoute: typeof LocationsNeighborhoodsCreateRoute
  LocationsNeighborhoodsEditRoute: typeof LocationsNeighborhoodsEditRoute
  LocationsBarsIndexRoute: typeof LocationsBarsIndexRoute
  LocationsCitiesIndexRoute: typeof LocationsCitiesIndexRoute
  LocationsClubsIndexRoute: typeof LocationsClubsIndexRoute
  LocationsNeighborhoodsIndexRoute: typeof LocationsNeighborhoodsIndexRoute
  LocationsBarsCategoriesCreateRoute: typeof LocationsBarsCategoriesCreateRoute
  LocationsBarsCategoriesEditRoute: typeof LocationsBarsCategoriesEditRoute
  LocationsClubsCategoriesCreateRoute: typeof LocationsClubsCategoriesCreateRoute
  LocationsClubsCategoriesEditRoute: typeof LocationsClubsCategoriesEditRoute
  LocationsBarsCategoriesIndexRoute: typeof LocationsBarsCategoriesIndexRoute
  LocationsClubsCategoriesIndexRoute: typeof LocationsClubsCategoriesIndexRoute
}

const LocationsRouteChildren: LocationsRouteChildren = {
  LocationsBarsCreateRoute: LocationsBarsCreateRoute,
  LocationsBarsEditRoute: LocationsBarsEditRoute,
  LocationsCitiesCreateRoute: LocationsCitiesCreateRoute,
  LocationsCitiesEditRoute: LocationsCitiesEditRoute,
  LocationsClubsCreateRoute: LocationsClubsCreateRoute,
  LocationsClubsEditRoute: LocationsClubsEditRoute,
  LocationsNeighborhoodsCreateRoute: LocationsNeighborhoodsCreateRoute,
  LocationsNeighborhoodsEditRoute: LocationsNeighborhoodsEditRoute,
  LocationsBarsIndexRoute: LocationsBarsIndexRoute,
  LocationsCitiesIndexRoute: LocationsCitiesIndexRoute,
  LocationsClubsIndexRoute: LocationsClubsIndexRoute,
  LocationsNeighborhoodsIndexRoute: LocationsNeighborhoodsIndexRoute,
  LocationsBarsCategoriesCreateRoute: LocationsBarsCategoriesCreateRoute,
  LocationsBarsCategoriesEditRoute: LocationsBarsCategoriesEditRoute,
  LocationsClubsCategoriesCreateRoute: LocationsClubsCategoriesCreateRoute,
  LocationsClubsCategoriesEditRoute: LocationsClubsCategoriesEditRoute,
  LocationsBarsCategoriesIndexRoute: LocationsBarsCategoriesIndexRoute,
  LocationsClubsCategoriesIndexRoute: LocationsClubsCategoriesIndexRoute,
}

const LocationsRouteWithChildren = LocationsRoute._addFileChildren(
  LocationsRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  LocationsRoute: LocationsRouteWithChildren,
  AuthLoginRoute: AuthLoginRoute,
  UsersIndexRoute: UsersIndexRoute,
  SettingsAppLayoutCreateRoute: SettingsAppLayoutCreateRoute,
  SettingsAppLayoutIndexRoute: SettingsAppLayoutIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
