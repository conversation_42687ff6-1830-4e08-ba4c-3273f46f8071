import axios from 'axios'
import { getDefaultStore } from 'jotai'
import { authAtom } from './atoms/auth.atom'
import { VITE_API_URL } from './env'

export function getToken() {
  return getDefaultStore().get(authAtom)?.auth?.access_token
}

export const client = axios.create({ baseURL: VITE_API_URL })

// Request Interceptor to attach token
client.interceptors.request.use(
  (config) => {
    const token = getToken() // Retrieve token from storage
    if (token) {
      config.headers.Authorization = `Bearer ${token}` // Inject token in headers
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

export const fetchData = <TData, TVariables>(
  query: string,
  variables?: TVariables,
  options?: Record<string, string>,
): (() => Promise<TData>) => {
  return async () => {
    const headers = {
      'Content-Type': 'application/json',
      ...options,
    }

    const response = await client.post(
      '/graphql',
      {
        query,
        variables,
      },
      { headers },
    )

    if (response.data.errors) {
      const { message } = response.data.errors[0] || {}
      throw new Error(message || 'Error…')
    }

    return response.data.data
  }
}

/** File Upload */

type UploadFile = {
  file: File
  parentFolderId?: string
  fileName?: string
}

type FileUploadResponse = {
  success: boolean
  message: string
  filePath: string
  fileId: string
}

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

export const uploadFile = async (
  { file, fileName, parentFolderId }: UploadFile,
  uploadProgress?: (progress: UploadProgress) => void,
): Promise<FileUploadResponse> => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('fileName', fileName || file.name)
  if (parentFolderId) formData.append('parentFolderId', parentFolderId)
  const { data } = await client.post<FileUploadResponse>(
    '/storage/upload',
    formData,
    {
      headers: { 'Content-Type': 'multipart/form-data' },
      onUploadProgress(progressEvent) {
        if (progressEvent.total) {
          const progress: UploadProgress = {
            loaded: progressEvent.loaded,
            total: progressEvent.total,
            percentage: Math.round(
              (progressEvent.loaded / progressEvent.total) * 100,
            ),
          }
          uploadProgress?.(progress)
        }
      },
    },
  )

  return data
}
