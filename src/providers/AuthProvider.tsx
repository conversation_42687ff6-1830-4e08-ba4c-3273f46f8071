import { useAuth } from '@/atoms/auth.atom'
import { useLocation, useRouter } from '@tanstack/react-router'
import { useEffect } from 'react'

export default function AuthProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const { auth, isLoading } = useAuth()
  const location = useLocation()
  const router = useRouter()

  const isLoggedIn = !!auth
  const isAuthRoute = location.pathname.startsWith('/auth')

  useEffect(() => {
    // Don't run navigation logic while still loading from storage
    if (isLoading) return

    // If user is not logged in and tries to access a protected route, redirect to login
    if (!isLoggedIn && !isAuthRoute) {
      router.navigate({
        to: '/auth/login',
        search: { from: location.pathname },
      })
    }

    // If user is logged in and tries to access the login page, redirect to home
    if (isLoggedIn && isAuthRoute) {
      const redirect = (location.search as any)?.from
      router.navigate({ to: redirect ?? '/' })
    }
  }, [auth, isLoading, isLoggedIn, isAuthRoute, router])

  // Show loading state while auth is being initialized from storage
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div>Loading...</div>
      </div>
    )
  }

  return <>{children}</>
}
