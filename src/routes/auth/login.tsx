import { LoginForm } from '@/forms/auth/login.form'
import { createFileRoute } from '@tanstack/react-router'
import { GalleryVerticalEnd } from 'lucide-react'

export const Route = createFileRoute('/auth/login')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <div className="grid min-h-svh lg:grid-cols-2">
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="flex justify-center gap-2 md:justify-start">
          <a href="#" className="flex items-center gap-2 font-medium">
            <div className="bg-primary text-primary-foreground flex size-6 items-center justify-center rounded-md">
              <GalleryVerticalEnd className="size-4" />
            </div>
            Acme Inc.
          </a>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-xs">
            <LoginForm />
          </div>
        </div>
      </div>
      <div className="bg-muted relative hidden lg:block">
        <img
          src="/login-banner.jpg"
          alt="Image"
          className="absolute inset-0 h-full w-full object-cover"
        />
        <div
          className="absolute inset-0"
          style={{
            backgroundBlendMode: 'multiply',
            background: `
              linear-gradient(99.88deg, rgba(247, 124, 62, 0.46) -4.91%, rgba(247, 124, 62, 0.46) 18.57%, rgba(243, 193, 96, 0.46) 45.97%, rgba(101, 128, 121, 0.46) 99.99%, rgba(29, 75, 101, 0.46) 149.31%)
            `.trim(),
          }}
        />
      </div>
    </div>
  )
}
