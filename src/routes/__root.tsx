import { createRootRouteWithContext } from '@tanstack/react-router'

import Header from '../components/header'
import { ThemeProvider } from '@/components/theme-provider'

import appCss from '../styles.css?url'

import BaseLayout from '@/layouts/base-layout'
import type { QueryClient } from '@tanstack/react-query'
import AuthProvider from '@/providers/AuthProvider'
import { Toaster } from 'react-hot-toast'
import { Suspense } from 'react'
import { PageLoadingFallback } from '@/components/loading-fallback'

// Breadcrumb interface for route context
export interface BreadcrumbItem {
  title: string
  href?: string
}

interface MyRouterContext {
  queryClient: QueryClient
}

// Extend the router context to include breadcrumb information
declare module '@tanstack/react-router' {
  interface RouteContext {
    breadcrumb?: BreadcrumbItem
  }
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  head: () => ({
    meta: [
      {
        charSet: 'utf-8',
      },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
      {
        title: 'Seeker Admin Dashboard',
      },
    ],
    links: [
      {
        rel: 'stylesheet',
        href: appCss,
      },
    ],
    scripts: [
      {
        children: `
          (function() {
            try {
              var theme = localStorage.getItem('vite-ui-theme') || 'dark';
              if (theme === 'system') {
                theme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
              }
              document.documentElement.classList.add(theme);
            } catch (e) {}
          })();
        `,
      },
    ],
  }),

  component: () => (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      <Header />
      <Suspense fallback={<PageLoadingFallback />}>
        <AuthProvider>
          <BaseLayout />
        </AuthProvider>
      </Suspense>

      <Toaster />
      {/* <TanStackRouterDevtools />
      <TanStackQueryLayout /> */}
    </ThemeProvider>
  ),

  notFoundComponent: () => <div>404</div>,
})
