import DataTables from '@/data-tables'
import { useSuspenseUsersQuery } from '@/generated/graphql'
import { usePagination } from '@/hooks/use-pagination'
import { createFileRoute } from '@tanstack/react-router'

export const Route = createFileRoute('/users/')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Users',
      href: '/users',
    },
  }),
})

function RouteComponent() {
  // Initialize pagination hook with configuration
  const pagination = usePagination({
    initialPage: 1,
    initialLimit: 10,
    limitOptions: [5, 10, 25, 50, 100],
  })

  // Fetch users data with pagination
  const usersQuery = useSuspenseUsersQuery({
    paginationInput: pagination.paginationInput,
  })

  return (
    <div className="flex-1">
      {/*
        Method 1: Use data table with integrated pagination (recommended)
        The data table component handles both table display and pagination
      */}
      <DataTables.UsersDataTable
        queryResult={usersQuery}
        onPageChange={pagination.onPageChange}
        onLimitChange={pagination.onLimitChange}
      />

      {/*
        Method 2: Alternative - Use separate Pagination component with spreading
        Uncomment below to use standalone pagination component:

        <Table>
          // Your table content here
        </Table>
        <Pagination paginationInfo={usersQuery.data.users} {...pagination} />
      */}
    </div>
  )
}
