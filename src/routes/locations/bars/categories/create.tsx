import { createFileRoute } from '@tanstack/react-router'
import BarCategoryForms from '@/forms/bar-category'
import { useCreateBarCategoryFormMethods } from '@/forms/bar-category/create-bar-category-form'

export const Route = createFileRoute('/locations/bars/categories/create')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Create Category',
    },
  }),
})

function RouteComponent() {
  const formMethods = useCreateBarCategoryFormMethods()

  return (
    <div className="flex-1">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Create New Bar Category</h1>
        <p className="text-muted-foreground">
          Add a new category to organize bars on the platform.
        </p>
      </div>
      <BarCategoryForms.CreateBarCategoryForm {...formMethods} />
    </div>
  )
}
