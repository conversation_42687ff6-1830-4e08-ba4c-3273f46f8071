import { Pagination } from '@/components/ui/pagination'
import { Button } from '@/components/ui/button'
import DataTables from '@/data-tables'
import { useSuspenseBarCategoriesQuery } from '@/generated/graphql'
import { usePagination } from '@/hooks'
import { createFileRoute, Link } from '@tanstack/react-router'
import { Plus } from 'lucide-react'

export const Route = createFileRoute('/locations/bars/categories/')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Categories',
      href: '/locations/bars/categories',
    },
  }),
})

function RouteComponent() {
  // Initialize pagination hook with configuration
  const pagination = usePagination()

  // Fetch bar categories data with pagination
  const barCategoriesQuery = useSuspenseBarCategoriesQuery({
    paginationInput: pagination.paginationInput,
    barCategoriesInput: {},
  })

  return (
    <div className="flex-1 space-y-4">
      {/* Header with Create Category Button */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Bar Categories</h1>
          <p className="text-muted-foreground">
            Manage bar categories used for organizing bars.
          </p>
        </div>
        <Button asChild>
          <Link to="/locations/bars/categories/create">
            <Plus className="mr-2 h-4 w-4" />
            Create Category
          </Link>
        </Button>
      </div>

      {/* Bar Categories data table with integrated pagination */}
      <DataTables.BarCategoriesDataTable {...barCategoriesQuery} />

      <Pagination
        {...pagination}
        paginationInfo={barCategoriesQuery.data.barCategories}
      />
    </div>
  )
}
