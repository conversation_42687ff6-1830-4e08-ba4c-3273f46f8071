import { createFileRoute } from '@tanstack/react-router'
import BarForms from '@/forms/bar'
import { useCreateBarFormMethods } from '@/forms/bar/create-bar-form'

export const Route = createFileRoute('/locations/bars/create')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Create Bar',
    },
  }),
})

function RouteComponent() {
  const formMethods = useCreateBarFormMethods()

  return (
    <div className="flex-1">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Create New Bar</h1>
        <p className="text-muted-foreground">
          Add a new bar to the platform with all necessary details.
        </p>
      </div>
      <BarForms.CreateBarForm {...formMethods} />
    </div>
  )
}
