import { createFileRoute } from '@tanstack/react-router'
import BarForms from '@/forms/bar'
import { useUpdateBarFormMethods } from '@/forms/bar/update-bar-form'
import { z } from 'zod'

const searchSchema = z.object({
  slug: z.string(),
})

export const Route = createFileRoute('/locations/bars/edit')({
  component: RouteComponent,
  validateSearch: searchSchema,
  beforeLoad: ({ search }) => ({
    breadcrumb: {
      title: `Edit Bar: ${search.slug}`,
    },
  }),
})

function RouteComponent() {
  const { slug } = Route.useSearch()
  const formMethods = useUpdateBarFormMethods(slug)

  return (
    <div className="flex-1">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Edit Bar</h1>
        <p className="text-muted-foreground">
          Update the bar information and settings.
        </p>
      </div>
      <BarForms.UpdateBarForm {...formMethods} />
    </div>
  )
}
