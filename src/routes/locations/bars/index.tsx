import { Pagination } from '@/components/ui/pagination'
import { Button } from '@/components/ui/button'
import DataTables from '@/data-tables'
import { useSuspenseBarsQuery } from '@/generated/graphql'
import { usePagination } from '@/hooks'
import { createFileRoute, Link } from '@tanstack/react-router'
import { Plus } from 'lucide-react'

export const Route = createFileRoute('/locations/bars/')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Bars',
      href: '/locations/bars',
    },
  }),
})

function RouteComponent() {
  // Initialize pagination hook with configuration
  const pagination = usePagination()

  // Fetch bars data with pagination
  const barsQuery = useSuspenseBarsQuery({
    barsInput: {},
    paginationInput: pagination.paginationInput,
  })

  return (
    <div className="flex-1 space-y-4">
      {/* Header with action buttons */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Bars</h1>
          <p className="text-muted-foreground">
            Manage bars and their categories on the platform.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link to="/locations/bars/categories">Categories</Link>
          </Button>
          <Button asChild>
            <Link to="/locations/bars/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Bar
            </Link>
          </Button>
        </div>
      </div>

      {/* Bars data table with integrated pagination */}
      <DataTables.BarsDataTable {...barsQuery} />

      <Pagination {...pagination} paginationInfo={barsQuery.data.bars} />
    </div>
  )
}
