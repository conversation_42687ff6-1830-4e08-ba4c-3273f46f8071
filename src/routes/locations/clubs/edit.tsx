import { createFileRoute } from '@tanstack/react-router'
import ClubForms from '@/forms/club'
import { useUpdateClubFormMethods } from '@/forms/club/update-club-form'

export const Route = createFileRoute('/locations/clubs/edit')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Edit Club',
    },
  }),
  validateSearch: (search: Record<string, unknown>) => {
    return {
      slug: (search.slug as string) || '',
    }
  },
})

function RouteComponent() {
  const { slug } = Route.useSearch()
  const formMethods = useUpdateClubFormMethods(slug)

  if (!slug) {
    return (
      <div className="flex-1 p-10">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Error</h1>
          <p className="text-muted-foreground">
            Club slug is required. Please provide a club slug in the URL query parameter.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1">
      <div className="mb-6 px-10 pt-6">
        <h1 className="text-2xl font-bold">Edit Club: {slug}</h1>
        <p className="text-muted-foreground">
          Update the club information below.
        </p>
      </div>
      <ClubForms.UpdateClubForm {...formMethods} />
    </div>
  )
}
