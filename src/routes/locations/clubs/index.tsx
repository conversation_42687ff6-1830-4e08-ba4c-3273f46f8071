import { Pagination } from '@/components/ui/pagination'
import { Button } from '@/components/ui/button'
import DataTables from '@/data-tables'
import { useSuspenseClubsQuery } from '@/generated/graphql'
import { usePagination } from '@/hooks'
import { createFileRoute, Link } from '@tanstack/react-router'
import { Plus } from 'lucide-react'

export const Route = createFileRoute('/locations/clubs/')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Clubs',
      href: '/locations/clubs',
    },
  }),
})

function RouteComponent() {
  // Initialize pagination hook with configuration
  const pagination = usePagination()

  // Fetch clubs data with pagination
  const clubsQuery = useSuspenseClubsQuery({
    clubsInput: {},
  })

  return (
    <div className="flex-1 space-y-4">
      {/* Header with action buttons */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Clubs</h1>
          <p className="text-muted-foreground">
            Manage clubs and their categories on the platform.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link to="/locations/clubs/categories">Categories</Link>
          </Button>
          <Button asChild>
            <Link to="/locations/clubs/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Club
            </Link>
          </Button>
        </div>
      </div>

      {/* Clubs data table with integrated pagination */}
      <DataTables.ClubsDataTable {...clubsQuery} />

      <Pagination {...pagination} paginationInfo={clubsQuery.data.clubs} />
    </div>
  )
}
