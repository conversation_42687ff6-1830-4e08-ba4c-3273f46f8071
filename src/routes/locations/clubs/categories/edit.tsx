import { createFileRoute } from '@tanstack/react-router'
import ClubCategoryForms from '@/forms/club-category'
import { useUpdateClubCategoryFormMethods } from '@/forms/club-category/update-club-category-form'

export const Route = createFileRoute('/locations/clubs/categories/edit')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Edit Category',
    },
  }),
  validateSearch: (search: Record<string, unknown>) => {
    return {
      name: (search.name as string) || '',
    }
  },
})

function RouteComponent() {
  const { name } = Route.useSearch()
  const formMethods = useUpdateClubCategoryFormMethods(name)

  if (!name) {
    return (
      <div className="flex-1 p-10">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Error</h1>
          <p className="text-muted-foreground">
            Club category name is required. Please provide a category name in the URL query
            parameter.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1">
      <div className="mb-6 px-10 pt-6">
        <h1 className="text-2xl font-bold">Edit Club Category: {name}</h1>
        <p className="text-muted-foreground">
          Update the club category information below.
        </p>
      </div>
      <ClubCategoryForms.UpdateClubCategoryForm {...formMethods} />
    </div>
  )
}
