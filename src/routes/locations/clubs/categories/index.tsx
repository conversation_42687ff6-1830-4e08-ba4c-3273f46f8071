import { Pagination } from '@/components/ui/pagination'
import { Button } from '@/components/ui/button'
import DataTables from '@/data-tables'
import { useSuspenseClubCategoriesQuery } from '@/generated/graphql'
import { usePagination } from '@/hooks'
import { createFileRoute, Link } from '@tanstack/react-router'
import { Plus } from 'lucide-react'

export const Route = createFileRoute('/locations/clubs/categories/')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Categories',
      href: '/locations/clubs/categories',
    },
  }),
})

function RouteComponent() {
  // Initialize pagination hook with configuration
  const pagination = usePagination()

  // Fetch club categories data with pagination
  const clubCategoriesQuery = useSuspenseClubCategoriesQuery({
    paginationInput: pagination.paginationInput,
    clubCategoriesInput: {},
  })

  return (
    <div className="flex-1 space-y-4">
      {/* Header with Create Category Button */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Club Categories</h1>
          <p className="text-muted-foreground">
            Manage club categories used for organizing clubs.
          </p>
        </div>
        <Button asChild>
          <Link to="/locations/clubs/categories/create">
            <Plus className="mr-2 h-4 w-4" />
            Create Category
          </Link>
        </Button>
      </div>

      {/* Club Categories data table with integrated pagination */}
      <DataTables.ClubCategoriesDataTable {...clubCategoriesQuery} />

      <Pagination
        {...pagination}
        paginationInfo={clubCategoriesQuery.data.clubCategories}
      />
    </div>
  )
}
