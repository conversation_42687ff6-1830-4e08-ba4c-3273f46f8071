import { createFileRoute } from '@tanstack/react-router'
import ClubCategoryForms from '@/forms/club-category'
import { useCreateClubCategoryFormMethods } from '@/forms/club-category/create-club-category-form'

export const Route = createFileRoute('/locations/clubs/categories/create')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Create Category',
    },
  }),
})

function RouteComponent() {
  const formMethods = useCreateClubCategoryFormMethods()

  return (
    <div className="flex-1">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Create New Club Category</h1>
        <p className="text-muted-foreground">
          Add a new category to organize clubs on the platform.
        </p>
      </div>
      <ClubCategoryForms.CreateClubCategoryForm {...formMethods} />
    </div>
  )
}
