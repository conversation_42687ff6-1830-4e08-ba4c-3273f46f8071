import { createFileRoute } from '@tanstack/react-router'
import ClubForms from '@/forms/club'
import { useCreateClubFormMethods } from '@/forms/club/create-club-form'

export const Route = createFileRoute('/locations/clubs/create')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Create Club',
    },
  }),
})

function RouteComponent() {
  const formMethods = useCreateClubFormMethods()

  return (
    <div className="flex-1">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Create New Club</h1>
        <p className="text-muted-foreground">
          Add a new club to the platform with all necessary details.
        </p>
      </div>
      <ClubForms.CreateClubForm {...formMethods} />
    </div>
  )
}
