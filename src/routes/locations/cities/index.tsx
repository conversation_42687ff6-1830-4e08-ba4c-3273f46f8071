import { Pagination } from '@/components/ui/pagination'
import { Button } from '@/components/ui/button'
import DataTables from '@/data-tables'
import { useSuspenseCitiesQuery } from '@/generated/graphql'
import { usePagination } from '@/hooks'
import { createFileRoute, Link } from '@tanstack/react-router'
import { Plus } from 'lucide-react'

export const Route = createFileRoute('/locations/cities/')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Cities',
      href: '/locations/cities',
    },
  }),
})

function RouteComponent() {
  // Initialize pagination hook with configuration
  const pagination = usePagination()

  // Fetch cities data with pagination
  const citiesQuery = useSuspenseCitiesQuery({
    paginationInput: pagination.paginationInput,
  })

  return (
    <div className="flex-1 space-y-4">
      {/* Create City Button */}
      <div className="flex justify-end">
        <Button asChild>
          <Link to="/locations/cities/create">
            <Plus className="mr-2 h-4 w-4" />
            Create City
          </Link>
        </Button>
      </div>

      {/* Cities data table with integrated pagination */}
      <DataTables.CitiesDataTable {...citiesQuery} />

      <Pagination {...pagination} paginationInfo={citiesQuery.data.cities} />
    </div>
  )
}
