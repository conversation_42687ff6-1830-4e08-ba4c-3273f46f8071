import { createFileRoute } from '@tanstack/react-router'
import CityForms from '@/forms/city'
import { useCreateCityFormMethods } from '@/forms/city/create-city.form'

export const Route = createFileRoute('/locations/cities/create')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Create City',
    },
  }),
})

function RouteComponent() {
  const formMethods = useCreateCityFormMethods()

  return (
    <div className="flex-1">
      <CityForms.CreateCityForm {...formMethods} />
    </div>
  )
}
