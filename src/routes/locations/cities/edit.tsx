import { createFileRoute } from '@tanstack/react-router'
import CityForms from '@/forms/city'
import { useUpdateCityFormMethods } from '@/forms/city/update-city.form'

export const Route = createFileRoute('/locations/cities/edit')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Edit City',
    },
  }),
  validateSearch: (search: Record<string, unknown>) => {
    return {
      name: (search.name as string) || '',
    }
  },
})

function RouteComponent() {
  const { name } = Route.useSearch()
  const formMethods = useUpdateCityFormMethods(name)

  if (!name) {
    return (
      <div className="flex-1 p-10">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Error</h1>
          <p className="text-muted-foreground">
            City name is required. Please provide a city name in the URL query
            parameter.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1">
      <div className="mb-6 px-10 pt-6">
        <h1 className="text-2xl font-bold">Edit City: {name}</h1>
        <p className="text-muted-foreground">
          Update the city information below.
        </p>
      </div>
      <CityForms.UpdateCityForm {...formMethods} />
    </div>
  )
}
