import { createFileRoute } from '@tanstack/react-router'
import NeighborhoodForms from '@/forms/neighborhood'
import { useUpdateNeighborhoodFormMethods } from '@/forms/neighborhood/update-neighborhood.form'

export const Route = createFileRoute('/locations/neighborhoods/edit')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Edit Neighborhood',
    },
  }),
  validateSearch: (search: Record<string, unknown>) => {
    return {
      name: (search.name as string) || '',
    }
  },
})

function RouteComponent() {
  const { name } = Route.useSearch()
  const formMethods = useUpdateNeighborhoodFormMethods(name)

  if (!name) {
    return (
      <div className="flex-1 p-10">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Error</h1>
          <p className="text-muted-foreground">
            Neighborhood name is required. Please provide a neighborhood name in the URL query parameter.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1">
      <div className="mb-6 px-10 pt-6">
        <h1 className="text-2xl font-bold">Edit Neighborhood: {name}</h1>
        <p className="text-muted-foreground">
          Update the neighborhood information below.
        </p>
      </div>
      <NeighborhoodForms.UpdateNeighborhoodForm {...formMethods} />
    </div>
  )
}
