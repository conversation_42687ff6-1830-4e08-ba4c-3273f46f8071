import { Pagination } from '@/components/ui/pagination'
import { Button } from '@/components/ui/button'
import DataTables from '@/data-tables'
import { useSuspenseNeighborhoodsQuery } from '@/generated/graphql'
import { usePagination } from '@/hooks'
import { createFileRoute, Link } from '@tanstack/react-router'
import { Plus } from 'lucide-react'

export const Route = createFileRoute('/locations/neighborhoods/')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Neighborhoods',
      href: '/locations/neighborhoods',
    },
  }),
})

function RouteComponent() {
  // Initialize pagination hook with configuration
  const pagination = usePagination()

  // Fetch neighborhoods data with pagination
  const neighborhoodsQuery = useSuspenseNeighborhoodsQuery({
    paginationInput: pagination.paginationInput,
  })

  return (
    <div className="flex-1 space-y-4">
      {/* Create Neighborhood Button */}
      <div className="flex justify-end">
        <Button asChild>
          <Link to="/locations/neighborhoods/create">
            <Plus className="mr-2 h-4 w-4" />
            Create Neighborhood
          </Link>
        </Button>
      </div>

      {/* Neighborhoods data table with integrated pagination */}
      <DataTables.NeighborhoodsDataTable {...neighborhoodsQuery} />

      <Pagination
        {...pagination}
        paginationInfo={neighborhoodsQuery.data.neighborhoods}
      />
    </div>
  )
}
