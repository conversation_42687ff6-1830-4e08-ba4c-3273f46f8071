import { createFileRoute } from '@tanstack/react-router'
import NeighborhoodForms from '@/forms/neighborhood'
import { useCreateNeighborhoodFormMethods } from '@/forms/neighborhood/create-neighborhood.form'

export const Route = createFileRoute('/locations/neighborhoods/create')({
  component: RouteComponent,
  beforeLoad: () => ({
    breadcrumb: {
      title: 'Create Neighborhood',
    },
  }),
})

function RouteComponent() {
  const formMethods = useCreateNeighborhoodFormMethods()

  return (
    <div className="flex-1">
      <NeighborhoodForms.CreateNeighborhoodForm {...formMethods} />
    </div>
  )
}
